//! Context Menu System for File Operations
//!
//! This module provides a comprehensive context menu system with dynamic actions,
//! keyboard shortcuts, and plugin extensibility.

use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::collections::HashMap;
use floem::{
    menu::{Menu, MenuItem},
    keyboard::{Key, Modifiers, NamedKey},
    action::show_context_menu,
    kurbo::Point,
};

use matrix_core::Engine as CoreEngine;
use crate::error::UiError;
use super::{FileOperationsEngine, FileOperationType};

/// Context menu action
#[derive(Debug, Clone)]
pub struct ContextMenuAction {
    pub id: String,
    pub label: String,
    pub icon: Option<String>,
    pub shortcut: Option<(Key, Modifiers)>,
    pub enabled: bool,
    pub separator_after: bool,
    pub action: ContextMenuActionType,
}

/// Types of context menu actions
#[derive(Debug, Clone)]
pub enum ContextMenuActionType {
    FileOperation(FileOperationType),
    Custom(String), // Custom action ID for plugin extensibility
    Submenu(Vec<ContextMenuAction>),
}

/// Keyboard shortcut registry for context menu actions
pub struct ShortcutRegistry {
    shortcuts: HashMap<(Key, Modifiers), String>, // Maps shortcut to action ID
    actions: HashMap<String, ContextMenuAction>,  // Maps action ID to action
}

impl ShortcutRegistry {
    /// Create new shortcut registry
    pub fn new() -> Self {
        Self {
            shortcuts: HashMap::new(),
            actions: HashMap::new(),
        }
    }

    /// Register a shortcut for an action
    pub fn register_shortcut(&mut self, action: ContextMenuAction) {
        if let Some((key, modifiers)) = &action.shortcut {
            self.shortcuts.insert((key.clone(), *modifiers), action.id.clone());
        }
        self.actions.insert(action.id.clone(), action);
    }

    /// Get action by shortcut
    pub fn get_action_by_shortcut(&self, key: &Key, modifiers: &Modifiers) -> Option<&ContextMenuAction> {
        self.shortcuts.get(&(key.clone(), *modifiers))
            .and_then(|action_id| self.actions.get(action_id))
    }

    /// Get action by ID
    pub fn get_action_by_id(&self, id: &str) -> Option<&ContextMenuAction> {
        self.actions.get(id)
    }
}

/// Plugin action handler for extensibility
pub trait PluginActionHandler: Send + Sync {
    /// Handle custom plugin action
    fn handle_action(&self, action_id: &str, context: &PluginActionContext) -> Result<(), UiError>;

    /// Get available actions for context
    fn get_actions_for_context(&self, context: &PluginActionContext) -> Vec<ContextMenuAction>;
}

/// Context provided to plugin actions
#[derive(Debug, Clone)]
pub struct PluginActionContext {
    pub selected_paths: Vec<PathBuf>,
    pub current_directory: Option<PathBuf>,
    pub editor_context: Option<String>, // Current file content, selection, etc.
}

impl PluginActionContext {
    /// Create new plugin action context
    pub fn new(selected_paths: Vec<PathBuf>, current_directory: Option<PathBuf>) -> Self {
        Self {
            selected_paths,
            current_directory,
            editor_context: None,
        }
    }

    /// Set editor context
    pub fn with_editor_context(mut self, context: String) -> Self {
        self.editor_context = Some(context);
        self
    }
}

/// Context menu builder
pub struct ContextMenuBuilder {
    actions: Vec<ContextMenuAction>,
    file_ops: Arc<FileOperationsEngine>,
    core: Arc<CoreEngine>,
    plugin_handlers: Vec<Arc<dyn PluginActionHandler>>,
    shortcut_registry: Arc<std::sync::RwLock<ShortcutRegistry>>,
}

impl ContextMenuBuilder {
    /// Create new context menu builder
    pub fn new(file_ops: Arc<FileOperationsEngine>, core: Arc<CoreEngine>) -> Self {
        Self {
            actions: Vec::new(),
            file_ops,
            core,
            plugin_handlers: Vec::new(),
            shortcut_registry: Arc::new(std::sync::RwLock::new(ShortcutRegistry::new())),
        }
    }

    /// Add plugin action handler
    pub fn add_plugin_handler(mut self, handler: Arc<dyn PluginActionHandler>) -> Self {
        self.plugin_handlers.push(handler);
        self
    }

    /// Get shortcut registry
    pub fn shortcut_registry(&self) -> Arc<std::sync::RwLock<ShortcutRegistry>> {
        self.shortcut_registry.clone()
    }

    /// Add plugin actions for context
    pub fn add_plugin_actions_for_context(mut self, context: &PluginActionContext) -> Self {
        // Collect actions from all plugin handlers
        for handler in &self.plugin_handlers {
            let plugin_actions = handler.get_actions_for_context(context);
            for action in plugin_actions {
                // Register shortcut if present
                if let Ok(mut registry) = self.shortcut_registry.write() {
                    registry.register_shortcut(action.clone());
                }
                self.actions.push(action);
            }
        }

        // Add separator if plugin actions were added
        if !self.plugin_handlers.is_empty() {
            self = self.add_separator();
        }

        self
    }

    /// Add file operation action
    pub fn add_file_operation(
        mut self,
        id: String,
        label: String,
        operation: FileOperationType,
        shortcut: Option<(Key, Modifiers)>,
    ) -> Self {
        self.actions.push(ContextMenuAction {
            id,
            label,
            icon: None,
            shortcut,
            enabled: true,
            separator_after: false,
            action: ContextMenuActionType::FileOperation(operation),
        });
        self
    }

    /// Add custom action
    pub fn add_custom_action(
        mut self,
        id: String,
        label: String,
        action_id: String,
        shortcut: Option<(Key, Modifiers)>,
    ) -> Self {
        self.actions.push(ContextMenuAction {
            id,
            label,
            icon: None,
            shortcut,
            enabled: true,
            separator_after: false,
            action: ContextMenuActionType::Custom(action_id),
        });
        self
    }

    /// Add separator
    pub fn add_separator(mut self) -> Self {
        if let Some(last_action) = self.actions.last_mut() {
            last_action.separator_after = true;
        }
        self
    }

    /// Add submenu
    pub fn add_submenu(
        mut self,
        id: String,
        label: String,
        submenu_actions: Vec<ContextMenuAction>,
    ) -> Self {
        self.actions.push(ContextMenuAction {
            id,
            label,
            icon: None,
            shortcut: None,
            enabled: true,
            separator_after: false,
            action: ContextMenuActionType::Submenu(submenu_actions),
        });
        self
    }

    /// Build context menu for file
    pub fn build_for_file(self, file_path: &Path) -> FileContextMenu {
        let mut builder = self;

        // Add standard file operations
        builder = builder
            .add_file_operation(
                "open".to_string(),
                "Open".to_string(),
                FileOperationType::Create { path: file_path.to_path_buf(), is_directory: false },
                Some((Key::Named(NamedKey::Enter), Modifiers::empty())),
            )
            .add_separator()
            .add_file_operation(
                "copy".to_string(),
                "Copy".to_string(),
                FileOperationType::Copy {
                    from: file_path.to_path_buf(),
                    to: file_path.with_extension("copy")
                },
                Some((Key::Character("c".into()), Modifiers::CONTROL)),
            )
            .add_file_operation(
                "duplicate".to_string(),
                "Duplicate".to_string(),
                FileOperationType::Duplicate {
                    source: file_path.to_path_buf(),
                    target: file_path.with_extension("copy")
                },
                Some((Key::Character("d".into()), Modifiers::CONTROL)),
            )
            .add_separator()
            .add_file_operation(
                "rename".to_string(),
                "Rename".to_string(),
                FileOperationType::Rename {
                    from: file_path.to_path_buf(),
                    to: file_path.to_path_buf()
                },
                Some((Key::Named(NamedKey::F2), Modifiers::empty())),
            )
            .add_file_operation(
                "delete".to_string(),
                "Delete".to_string(),
                FileOperationType::Delete { path: file_path.to_path_buf() },
                Some((Key::Named(NamedKey::Delete), Modifiers::empty())),
            );

        // Add plugin actions
        builder = builder.add_plugin_actions_for_context(&PluginActionContext::new(
            vec![file_path.to_path_buf()],
            file_path.parent().map(|p| p.to_path_buf()),
        ));

        FileContextMenu::new(builder.actions, builder.file_ops, builder.core)
    }

    /// Build context menu for directory
    pub fn build_for_directory(self, dir_path: &Path) -> FileContextMenu {
        let mut builder = self;
        
        // Add standard directory operations
        builder = builder
            .add_file_operation(
                "new_file".to_string(),
                "New File".to_string(),
                FileOperationType::Create { 
                    path: dir_path.join("new_file.txt"), 
                    is_directory: false 
                },
                Some((Key::Character("n".into()), Modifiers::CONTROL)),
            )
            .add_file_operation(
                "new_folder".to_string(),
                "New Folder".to_string(),
                FileOperationType::Create { 
                    path: dir_path.join("new_folder"), 
                    is_directory: true 
                },
                Some((Key::Character("n".into()), Modifiers::CONTROL | Modifiers::SHIFT)),
            )
            .add_separator()
            .add_file_operation(
                "copy".to_string(),
                "Copy".to_string(),
                FileOperationType::Copy { 
                    from: dir_path.to_path_buf(), 
                    to: dir_path.with_extension("copy") 
                },
                Some((Key::Character("c".into()), Modifiers::CONTROL)),
            )
            .add_file_operation(
                "duplicate".to_string(),
                "Duplicate".to_string(),
                FileOperationType::Duplicate { 
                    source: dir_path.to_path_buf(), 
                    target: dir_path.with_extension("copy") 
                },
                Some((Key::Character("d".into()), Modifiers::CONTROL)),
            )
            .add_separator()
            .add_file_operation(
                "rename".to_string(),
                "Rename".to_string(),
                FileOperationType::Rename { 
                    from: dir_path.to_path_buf(), 
                    to: dir_path.to_path_buf() 
                },
                Some((Key::Named(NamedKey::F2), Modifiers::empty())),
            )
            .add_file_operation(
                "delete".to_string(),
                "Delete".to_string(),
                FileOperationType::Delete { path: dir_path.to_path_buf() },
                Some((Key::Named(NamedKey::Delete), Modifiers::empty())),
            );

        FileContextMenu::new(builder.actions, builder.file_ops, builder.core)
    }

    /// Build context menu for multiple selection
    pub fn build_for_multiple_selection(self, paths: &[PathBuf]) -> FileContextMenu {
        let mut builder = self;
        
        // Add operations that work on multiple files
        builder = builder
            .add_custom_action(
                "copy_all".to_string(),
                format!("Copy {} items", paths.len()),
                "copy_multiple".to_string(),
                Some((Key::Character("c".into()), Modifiers::CONTROL)),
            )
            .add_custom_action(
                "delete_all".to_string(),
                format!("Delete {} items", paths.len()),
                "delete_multiple".to_string(),
                Some((Key::Named(NamedKey::Delete), Modifiers::empty())),
            );

        FileContextMenu::new(builder.actions, builder.file_ops, builder.core)
    }
}

/// File context menu
pub struct FileContextMenu {
    actions: Vec<ContextMenuAction>,
    file_ops: Arc<FileOperationsEngine>,
    core: Arc<CoreEngine>,
}

impl FileContextMenu {
    /// Create new file context menu
    pub fn new(
        actions: Vec<ContextMenuAction>,
        file_ops: Arc<FileOperationsEngine>,
        core: Arc<CoreEngine>,
    ) -> Self {
        Self {
            actions,
            file_ops,
            core,
        }
    }

    /// Show context menu at position
    pub fn show_at_position(&self, x: f64, y: f64) {
        let menu = self.build_floem_menu();
        show_context_menu(menu, Some(Point::new(x, y)));
    }

    /// Build Floem menu from actions
    fn build_floem_menu(&self) -> Menu {
        let mut menu = Menu::new("");
        
        for action in &self.actions {
            match &action.action {
                ContextMenuActionType::FileOperation(operation) => {
                    let file_ops = self.file_ops.clone();
                    let operation = operation.clone();
                    
                    let item = MenuItem::new(&action.label).action(move || {
                        let file_ops = file_ops.clone();
                        let operation = operation.clone();
                        tokio::spawn(async move {
                            if let Err(e) = file_ops.execute_operation(operation).await {
                                eprintln!("File operation failed: {}", e);
                            }
                        });
                    });
                    
                    menu = menu.entry(item);
                }
                ContextMenuActionType::Custom(action_id) => {
                    let core = self.core.clone();
                    let action_id = action_id.clone();
                    
                    let item = MenuItem::new(&action.label).action(move || {
                        // Emit custom action event
                        let event = matrix_core::Event::Custom {
                            source: "context_menu".to_string(),
                            name: "custom_action".to_string(),
                            data: serde_json::json!({ "action_id": action_id }),
                        };
                        let _ = core.event_bus().emit(event);
                    });
                    
                    menu = menu.entry(item);
                }
                ContextMenuActionType::Submenu(submenu_actions) => {
                    // Build proper submenu
                    let submenu = self.build_submenu(submenu_actions);
                    menu = menu.entry(submenu);
                }
            }
            
            if action.separator_after {
                menu = menu.separator();
            }
        }
        
        menu
    }

    /// Build submenu from actions
    fn build_submenu(&self, actions: &[ContextMenuAction]) -> Menu {
        let mut submenu = Menu::new("");

        for action in actions {
            match &action.action {
                ContextMenuActionType::FileOperation(operation) => {
                    let file_ops = self.file_ops.clone();
                    let operation = operation.clone();

                    let item = MenuItem::new(&action.label).action(move || {
                        let file_ops = file_ops.clone();
                        let operation = operation.clone();
                        tokio::spawn(async move {
                            if let Err(e) = file_ops.execute_operation(operation).await {
                                eprintln!("File operation failed: {}", e);
                            }
                        });
                    });

                    submenu = submenu.entry(item);
                }
                ContextMenuActionType::Custom(action_id) => {
                    let core = self.core.clone();
                    let action_id = action_id.clone();

                    let item = MenuItem::new(&action.label).action(move || {
                        // Emit custom action event
                        let event = matrix_core::Event::Custom {
                            source: "context_menu".to_string(),
                            name: "custom_action".to_string(),
                            data: serde_json::json!({ "action_id": action_id }),
                        };
                        let _ = core.event_bus().emit(event);
                    });

                    submenu = submenu.entry(item);
                }
                ContextMenuActionType::Submenu(nested_actions) => {
                    // Recursive submenu creation
                    let nested_submenu = self.build_submenu(nested_actions);
                    submenu = submenu.entry(nested_submenu);
                }
            }

            if action.separator_after {
                submenu = submenu.separator();
            }
        }

        submenu
    }

    /// Get action by ID
    pub fn get_action(&self, id: &str) -> Option<&ContextMenuAction> {
        self.actions.iter().find(|action| action.id == id)
    }

    /// Execute action by ID
    pub async fn execute_action(&self, id: &str) -> Result<(), UiError> {
        if let Some(action) = self.get_action(id) {
            match &action.action {
                ContextMenuActionType::FileOperation(operation) => {
                    self.file_ops.execute_operation(operation.clone()).await?;
                }
                ContextMenuActionType::Custom(action_id) => {
                    // Emit custom action event
                    let event = matrix_core::Event::Custom {
                        source: "context_menu".to_string(),
                        name: "custom_action".to_string(),
                        data: serde_json::json!({ "action_id": action_id }),
                    };
                    let _ = self.core.event_bus().emit(event);
                }
                ContextMenuActionType::Submenu(_) => {
                    return Err(UiError::FileOperation("Cannot execute submenu action".to_string()));
                }
            }
            Ok(())
        } else {
            Err(UiError::FileOperation(format!("Action not found: {}", id)))
        }
    }
}
