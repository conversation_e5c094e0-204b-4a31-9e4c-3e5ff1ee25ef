//! Progress Tracking System for File Operations
//!
//! This module provides comprehensive progress tracking with real-time updates,
//! bandwidth monitoring, and ETA calculations.

use std::time::{Duration, Instant, SystemTime, UNIX_EPOCH};
use std::collections::VecDeque;
use uuid::Uuid;

use super::{FileOperationProgress, FileOperationsEngine};

/// Progress tracking statistics
#[derive(Debu<PERSON>, Clone)]
pub struct ProgressStats {
    pub operation_id: Uuid,
    pub start_time: Instant,
    pub elapsed_time: Duration,
    pub estimated_total_time: Option<Duration>,
    pub estimated_remaining_time: Option<Duration>,
    pub average_speed_bytes_per_sec: f64,
    pub current_speed_bytes_per_sec: f64,
    pub speed_history: VecDeque<(Instant, u64)>, // (timestamp, bytes_processed)
}

impl ProgressStats {
    /// Create new progress stats
    pub fn new(operation_id: Uuid) -> Self {
        Self {
            operation_id,
            start_time: Instant::now(),
            elapsed_time: Duration::ZERO,
            estimated_total_time: None,
            estimated_remaining_time: None,
            average_speed_bytes_per_sec: 0.0,
            current_speed_bytes_per_sec: 0.0,
            speed_history: VecDeque::new(),
        }
    }

    /// Update progress statistics
    pub fn update(&mut self, progress: &FileOperationProgress) {
        self.elapsed_time = self.start_time.elapsed();
        
        // Add current progress to speed history
        self.speed_history.push_back((Instant::now(), progress.bytes_processed));
        
        // Keep only last 10 seconds of history for speed calculation
        let cutoff_time = Instant::now() - Duration::from_secs(10);
        while let Some((timestamp, _)) = self.speed_history.front() {
            if *timestamp < cutoff_time {
                self.speed_history.pop_front();
            } else {
                break;
            }
        }

        // Calculate average speed
        if self.elapsed_time.as_secs() > 0 {
            self.average_speed_bytes_per_sec = progress.bytes_processed as f64 / self.elapsed_time.as_secs_f64();
        }

        // Calculate current speed (based on recent history)
        if self.speed_history.len() >= 2 {
            let (first_time, first_bytes) = self.speed_history.front().unwrap();
            let (last_time, last_bytes) = self.speed_history.back().unwrap();
            
            let time_diff = last_time.duration_since(*first_time).as_secs_f64();
            if time_diff > 0.0 {
                self.current_speed_bytes_per_sec = (last_bytes - first_bytes) as f64 / time_diff;
            }
        }

        // Estimate remaining time
        if progress.total_bytes > 0 && self.current_speed_bytes_per_sec > 0.0 {
            let remaining_bytes = progress.total_bytes - progress.bytes_processed;
            let estimated_remaining_seconds = remaining_bytes as f64 / self.current_speed_bytes_per_sec;
            self.estimated_remaining_time = Some(Duration::from_secs_f64(estimated_remaining_seconds));
            
            // Estimate total time
            let estimated_total_seconds = progress.total_bytes as f64 / self.current_speed_bytes_per_sec;
            self.estimated_total_time = Some(Duration::from_secs_f64(estimated_total_seconds));
        }
    }

    /// Get human-readable speed string
    pub fn get_speed_string(&self) -> String {
        format_bytes_per_second(self.current_speed_bytes_per_sec)
    }

    /// Get human-readable ETA string
    pub fn get_eta_string(&self) -> String {
        match self.estimated_remaining_time {
            Some(duration) => format_duration(duration),
            None => "Unknown".to_string(),
        }
    }

    /// Get human-readable elapsed time string
    pub fn get_elapsed_string(&self) -> String {
        format_duration(self.elapsed_time)
    }
}

/// Enhanced progress tracking for file operations
#[derive(Debug, Clone)]
pub struct EnhancedProgress {
    pub base_progress: FileOperationProgress,
    pub stats: ProgressStats,
    pub sub_operations: Vec<FileOperationProgress>,
    pub warnings: Vec<String>,
    pub detailed_status: String,
}

impl EnhancedProgress {
    /// Create new enhanced progress
    pub fn new(base_progress: FileOperationProgress) -> Self {
        let stats = ProgressStats::new(base_progress.operation_id);
        
        Self {
            base_progress,
            stats,
            sub_operations: Vec::new(),
            warnings: Vec::new(),
            detailed_status: "Starting operation...".to_string(),
        }
    }

    /// Update progress with new data
    pub fn update(&mut self, progress: FileOperationProgress) {
        self.stats.update(&progress);
        self.base_progress = progress;
        
        // Update detailed status based on progress
        self.detailed_status = match &self.base_progress.current_file {
            Some(file) => format!("Processing: {}", file.display()),
            None => "Processing...".to_string(),
        };
    }

    /// Add a warning message
    pub fn add_warning(&mut self, warning: String) {
        self.warnings.push(warning);
    }

    /// Add sub-operation progress
    pub fn add_sub_operation(&mut self, sub_progress: FileOperationProgress) {
        self.sub_operations.push(sub_progress);
    }

    /// Get overall completion percentage including sub-operations
    pub fn get_overall_percentage(&self) -> f32 {
        if self.sub_operations.is_empty() {
            return self.base_progress.percentage;
        }

        let total_percentage: f32 = self.sub_operations.iter()
            .map(|op| op.percentage)
            .sum();
        
        let average_sub_percentage = total_percentage / self.sub_operations.len() as f32;
        
        // Weight main operation at 70%, sub-operations at 30%
        (self.base_progress.percentage * 0.7) + (average_sub_percentage * 0.3)
    }
}

impl FileOperationsEngine {
    /// Get enhanced progress for an operation
    pub async fn get_enhanced_progress(&self, operation_id: Uuid) -> Option<EnhancedProgress> {
        let progress = self.get_operation_progress(operation_id).await?;
        Some(EnhancedProgress::new(progress))
    }

    /// Create progress reporter for long-running operations
    pub async fn create_progress_reporter(&self, operation_id: Uuid) -> ProgressReporter {
        ProgressReporter::new(operation_id, self.active_operations.clone())
    }
}

/// Progress reporter for real-time updates
pub struct ProgressReporter {
    operation_id: Uuid,
    active_operations: std::sync::Arc<tokio::sync::RwLock<std::collections::HashMap<Uuid, FileOperationProgress>>>,
    last_update: Instant,
    update_interval: Duration,
}

impl ProgressReporter {
    /// Create new progress reporter
    pub fn new(
        operation_id: Uuid,
        active_operations: std::sync::Arc<tokio::sync::RwLock<std::collections::HashMap<Uuid, FileOperationProgress>>>,
    ) -> Self {
        Self {
            operation_id,
            active_operations,
            last_update: Instant::now(),
            update_interval: Duration::from_millis(100), // Update every 100ms
        }
    }

    /// Report progress update
    pub async fn report_progress<F>(&mut self, updater: F)
    where
        F: FnOnce(&mut FileOperationProgress),
    {
        let now = Instant::now();
        if now.duration_since(self.last_update) >= self.update_interval {
            let mut operations = self.active_operations.write().await;
            if let Some(progress) = operations.get_mut(&self.operation_id) {
                updater(progress);
                self.last_update = now;
            }
        }
    }

    /// Set update interval
    pub fn set_update_interval(&mut self, interval: Duration) {
        self.update_interval = interval;
    }

    /// Force immediate update
    pub async fn force_update<F>(&mut self, updater: F)
    where
        F: FnOnce(&mut FileOperationProgress),
    {
        let mut operations = self.active_operations.write().await;
        if let Some(progress) = operations.get_mut(&self.operation_id) {
            updater(progress);
            self.last_update = Instant::now();
        }
    }
}

/// Format bytes per second as human-readable string
fn format_bytes_per_second(bytes_per_sec: f64) -> String {
    const UNITS: &[&str] = &["B/s", "KB/s", "MB/s", "GB/s", "TB/s"];
    
    if bytes_per_sec == 0.0 {
        return "0 B/s".to_string();
    }
    
    let mut size = bytes_per_sec;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    format!("{:.1} {}", size, UNITS[unit_index])
}

/// Format duration as human-readable string
fn format_duration(duration: Duration) -> String {
    let total_seconds = duration.as_secs();
    
    if total_seconds < 60 {
        format!("{}s", total_seconds)
    } else if total_seconds < 3600 {
        let minutes = total_seconds / 60;
        let seconds = total_seconds % 60;
        format!("{}m {}s", minutes, seconds)
    } else {
        let hours = total_seconds / 3600;
        let minutes = (total_seconds % 3600) / 60;
        let seconds = total_seconds % 60;
        format!("{}h {}m {}s", hours, minutes, seconds)
    }
}

/// Format bytes as human-readable string
pub fn format_bytes(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    
    if bytes == 0 {
        return "0 B".to_string();
    }
    
    let mut size = bytes as f64;
    let mut unit_index = 0;
    
    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }
    
    format!("{:.1} {}", size, UNITS[unit_index])
}
