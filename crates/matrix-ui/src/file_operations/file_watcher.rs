//! File Watcher for automatic file system monitoring
//!
//! This module provides file system watching capabilities to automatically
//! detect changes and update the UI accordingly.

use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::{RwLock, mpsc};
use notify::{Watcher, RecursiveMode, Event as NotifyEvent, EventKind};
use uuid::Uuid;
use serde::{Serialize, Deserialize};

use matrix_core::{Engine as CoreEngine, Event};
use crate::error::UiError;

/// File system event types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FileSystemEvent {
    /// File or directory created
    Created { path: PathBuf },
    /// File or directory modified
    Modified { path: PathBuf },
    /// File or directory deleted
    Deleted { path: PathBuf },
    /// File or directory moved/renamed
    Moved { from: PathBuf, to: PathBuf },
}

/// File watcher configuration
#[derive(Debug, <PERSON><PERSON>)]
pub struct FileWatcherConfig {
    /// Whether to watch subdirectories recursively
    pub recursive: bool,
    /// File patterns to ignore (glob patterns)
    pub ignore_patterns: Vec<String>,
    /// Maximum number of events to buffer
    pub buffer_size: usize,
    /// Debounce time in milliseconds
    pub debounce_ms: u64,
}

impl Default for FileWatcherConfig {
    fn default() -> Self {
        Self {
            recursive: true,
            ignore_patterns: vec![
                "*.tmp".to_string(),
                "*.swp".to_string(),
                ".git/**".to_string(),
                "node_modules/**".to_string(),
                "target/**".to_string(),
            ],
            buffer_size: 1000,
            debounce_ms: 100,
        }
    }
}

/// File watcher for monitoring file system changes
pub struct FileWatcher {
    /// Core engine reference
    core: Arc<CoreEngine>,
    
    /// Active watchers by path
    watchers: Arc<RwLock<HashMap<PathBuf, notify::RecommendedWatcher>>>,
    
    /// Event sender
    event_sender: Arc<RwLock<Option<mpsc::UnboundedSender<FileSystemEvent>>>>,
    
    /// Configuration
    config: FileWatcherConfig,
    
    /// Watch IDs for tracking
    watch_ids: Arc<RwLock<HashMap<PathBuf, Uuid>>>,
}

impl FileWatcher {
    /// Create a new file watcher
    pub fn new(core: Arc<CoreEngine>, config: FileWatcherConfig) -> Self {
        Self {
            core,
            watchers: Arc::new(RwLock::new(HashMap::new())),
            event_sender: Arc::new(RwLock::new(None)),
            config,
            watch_ids: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    /// Start watching a directory
    pub async fn watch_directory(&self, path: &Path) -> Result<Uuid, UiError> {
        let watch_id = Uuid::new_v4();
        
        // Create event channel
        let (tx, mut rx) = mpsc::unbounded_channel();
        *self.event_sender.write().await = Some(tx.clone());

        // Create watcher
        let core = self.core.clone();
        let config = self.config.clone();
        let path_buf = path.to_path_buf();
        
        let mut watcher = notify::recommended_watcher(move |res: Result<NotifyEvent, notify::Error>| {
            match res {
                Ok(event) => {
                    if let Some(fs_event) = Self::convert_notify_event(event, &config) {
                        let _ = tx.send(fs_event);
                    }
                }
                Err(e) => {
                    eprintln!("File watcher error: {:?}", e);
                }
            }
        }).map_err(|e| UiError::FileOperation(format!("Failed to create watcher: {}", e)))?;

        // Start watching
        let mode = if self.config.recursive {
            RecursiveMode::Recursive
        } else {
            RecursiveMode::NonRecursive
        };

        watcher.watch(path, mode)
            .map_err(|e| UiError::FileOperation(format!("Failed to watch directory: {}", e)))?;

        // Store watcher
        self.watchers.write().await.insert(path_buf.clone(), watcher);
        self.watch_ids.write().await.insert(path_buf, watch_id);

        // Start event processing task
        let core_clone = core.clone();
        tokio::spawn(async move {
            while let Some(event) = rx.recv().await {
                // Emit event to core engine
                let core_event = Event::Custom {
                    source: "file_watcher".to_string(),
                    name: "file_system_event".to_string(),
                    data: serde_json::to_value(&event).unwrap_or_default(),
                };
                let _ = core_clone.event_bus().emit(core_event);
            }
        });

        Ok(watch_id)
    }

    /// Stop watching a directory
    pub async fn stop_watching(&self, path: &Path) -> Result<(), UiError> {
        self.watchers.write().await.remove(path);
        self.watch_ids.write().await.remove(path);
        Ok(())
    }

    /// Stop all watchers
    pub async fn stop_all(&self) -> Result<(), UiError> {
        self.watchers.write().await.clear();
        self.watch_ids.write().await.clear();
        *self.event_sender.write().await = None;
        Ok(())
    }

    /// Get all watched paths
    pub async fn get_watched_paths(&self) -> Vec<PathBuf> {
        self.watchers.read().await.keys().cloned().collect()
    }

    /// Check if a path is being watched
    pub async fn is_watching(&self, path: &Path) -> bool {
        self.watchers.read().await.contains_key(path)
    }

    /// Convert notify event to our file system event
    fn convert_notify_event(event: NotifyEvent, config: &FileWatcherConfig) -> Option<FileSystemEvent> {
        // Filter out ignored patterns
        for path in &event.paths {
            if Self::should_ignore_path(path, &config.ignore_patterns) {
                return None;
            }
        }

        match event.kind {
            EventKind::Create(_) => {
                if let Some(path) = event.paths.first() {
                    Some(FileSystemEvent::Created { path: path.clone() })
                } else {
                    None
                }
            }
            EventKind::Modify(_) => {
                if let Some(path) = event.paths.first() {
                    Some(FileSystemEvent::Modified { path: path.clone() })
                } else {
                    None
                }
            }
            EventKind::Remove(_) => {
                if let Some(path) = event.paths.first() {
                    Some(FileSystemEvent::Deleted { path: path.clone() })
                } else {
                    None
                }
            }
            _ => None,
        }
    }

    /// Check if path should be ignored based on patterns
    fn should_ignore_path(path: &Path, patterns: &[String]) -> bool {
        let path_str = path.to_string_lossy();

        for pattern in patterns {
            if let Ok(glob_pattern) = glob::Pattern::new(pattern) {
                if glob_pattern.matches(&path_str) {
                    return true;
                }
            }
        }

        false
    }
}

impl Drop for FileWatcher {
    fn drop(&mut self) {
        // Clean up is handled by the async methods
    }
}
