//! File Operations Implementation
//!
//! This module contains the actual file system operations with atomic behavior
//! and proper error handling.

use std::path::{Path, PathBuf};
use std::fs;
use std::io;
use tokio::fs as async_fs;
use uuid::Uuid;

use crate::error::UiError;
use super::{FileOperationsEngine, FileOperationType, FileOperationProgress};

impl FileOperationsEngine {
    /// Create a file or directory
    pub(super) async fn create_file_or_directory(
        &self,
        operation_id: Uuid,
        path: &Path,
        is_directory: bool,
    ) -> Result<Vec<PathBuf>, UiError> {
        // Check for cancellation
        if let Some(token) = self.get_cancellation_token(operation_id).await {
            if token.is_cancelled() {
                return Err(UiError::FileOperation("Operation cancelled".to_string()));
            }
        }

        self.update_progress(operation_id, |progress| {
            progress.current_file = Some(path.to_path_buf());
            progress.percentage = 50.0;
        }).await;

        // Check for cancellation again
        if let Some(token) = self.get_cancellation_token(operation_id).await {
            if token.is_cancelled() {
                return Err(UiError::FileOperation("Operation cancelled".to_string()));
            }
        }

        let result = if is_directory {
            async_fs::create_dir_all(path).await
        } else {
            // Create parent directories if they don't exist
            if let Some(parent) = path.parent() {
                async_fs::create_dir_all(parent).await?;
            }
            async_fs::File::create(path).await.map(|_| ())
        };

        match result {
            Ok(_) => {
                self.update_progress(operation_id, |progress| {
                    progress.processed_files = 1;
                    progress.percentage = 100.0;
                }).await;
                Ok(vec![path.to_path_buf()])
            }
            Err(e) => Err(UiError::FileOperation(format!("Failed to create {}: {}", path.display(), e))),
        }
    }

    /// Delete a file or directory
    pub(super) async fn delete_file_or_directory(
        &self,
        operation_id: Uuid,
        path: &Path,
    ) -> Result<Vec<PathBuf>, UiError> {
        self.update_progress(operation_id, |progress| {
            progress.current_file = Some(path.to_path_buf());
            progress.percentage = 25.0;
        }).await;

        // Check if path exists
        if !path.exists() {
            return Err(UiError::FileOperation(format!("Path does not exist: {}", path.display())));
        }

        let mut affected_paths = Vec::new();

        // Collect all paths that will be affected (for undo)
        if path.is_dir() {
            affected_paths.extend(self.collect_directory_paths(path)?);
        } else {
            affected_paths.push(path.to_path_buf());
        }

        self.update_progress(operation_id, |progress| {
            progress.total_files = affected_paths.len();
            progress.percentage = 50.0;
        }).await;

        // Perform deletion
        let result = if path.is_dir() {
            async_fs::remove_dir_all(path).await
        } else {
            async_fs::remove_file(path).await
        };

        match result {
            Ok(_) => {
                self.update_progress(operation_id, |progress| {
                    progress.processed_files = affected_paths.len();
                    progress.percentage = 100.0;
                }).await;
                Ok(affected_paths)
            }
            Err(e) => Err(UiError::FileOperation(format!("Failed to delete {}: {}", path.display(), e))),
        }
    }

    /// Rename a file or directory
    pub(super) async fn rename_file_or_directory(
        &self,
        operation_id: Uuid,
        from: &Path,
        to: &Path,
    ) -> Result<Vec<PathBuf>, UiError> {
        self.update_progress(operation_id, |progress| {
            progress.current_file = Some(from.to_path_buf());
            progress.percentage = 25.0;
        }).await;

        // Check if source exists
        if !from.exists() {
            return Err(UiError::FileOperation(format!("Source path does not exist: {}", from.display())));
        }

        // Check if destination already exists
        if to.exists() {
            return Err(UiError::FileOperation(format!("Destination already exists: {}", to.display())));
        }

        // Create parent directory if needed
        if let Some(parent) = to.parent() {
            async_fs::create_dir_all(parent).await?;
        }

        self.update_progress(operation_id, |progress| {
            progress.percentage = 75.0;
        }).await;

        match async_fs::rename(from, to).await {
            Ok(_) => {
                self.update_progress(operation_id, |progress| {
                    progress.processed_files = 1;
                    progress.percentage = 100.0;
                }).await;
                Ok(vec![from.to_path_buf(), to.to_path_buf()])
            }
            Err(e) => Err(UiError::FileOperation(format!("Failed to rename {} to {}: {}", from.display(), to.display(), e))),
        }
    }

    /// Move a file or directory
    pub(super) async fn move_file_or_directory(
        &self,
        operation_id: Uuid,
        from: &Path,
        to: &Path,
    ) -> Result<Vec<PathBuf>, UiError> {
        // For now, move is the same as rename
        self.rename_file_or_directory(operation_id, from, to).await
    }

    /// Copy a file or directory
    pub(super) async fn copy_file_or_directory(
        &self,
        operation_id: Uuid,
        from: &Path,
        to: &Path,
    ) -> Result<Vec<PathBuf>, UiError> {
        self.update_progress(operation_id, |progress| {
            progress.current_file = Some(from.to_path_buf());
            progress.percentage = 10.0;
        }).await;

        // Check if source exists
        if !from.exists() {
            return Err(UiError::FileOperation(format!("Source path does not exist: {}", from.display())));
        }

        let mut affected_paths = Vec::new();

        if from.is_dir() {
            affected_paths.extend(self.copy_directory_recursive(operation_id, from, to).await?);
        } else {
            affected_paths.extend(self.copy_file(operation_id, from, to).await?);
        }

        Ok(affected_paths)
    }

    /// Duplicate a file or directory
    pub(super) async fn duplicate_file_or_directory(
        &self,
        operation_id: Uuid,
        source: &Path,
        target: &Path,
    ) -> Result<Vec<PathBuf>, UiError> {
        self.copy_file_or_directory(operation_id, source, target).await
    }

    /// Copy a single file
    async fn copy_file(
        &self,
        operation_id: Uuid,
        from: &Path,
        to: &Path,
    ) -> Result<Vec<PathBuf>, UiError> {
        // Create parent directory if needed
        if let Some(parent) = to.parent() {
            async_fs::create_dir_all(parent).await?;
        }

        self.update_progress(operation_id, |progress| {
            progress.current_file = Some(from.to_path_buf());
            progress.percentage = 50.0;
        }).await;

        match async_fs::copy(from, to).await {
            Ok(_) => {
                self.update_progress(operation_id, |progress| {
                    progress.processed_files = 1;
                    progress.percentage = 100.0;
                }).await;
                Ok(vec![to.to_path_buf()])
            }
            Err(e) => Err(UiError::FileOperation(format!("Failed to copy {} to {}: {}", from.display(), to.display(), e))),
        }
    }

    /// Copy directory recursively
    async fn copy_directory_recursive(
        &self,
        operation_id: Uuid,
        from: &Path,
        to: &Path,
    ) -> Result<Vec<PathBuf>, UiError> {
        let mut affected_paths = Vec::new();

        // Create destination directory
        async_fs::create_dir_all(to).await?;
        affected_paths.push(to.to_path_buf());

        // Read source directory
        let mut entries = async_fs::read_dir(from).await?;
        let mut total_entries = 0;
        let mut processed_entries = 0;

        // Count total entries for progress tracking
        while let Some(_) = entries.next_entry().await? {
            total_entries += 1;
        }

        // Reset entries iterator
        entries = async_fs::read_dir(from).await?;

        self.update_progress(operation_id, |progress| {
            progress.total_files = total_entries;
        }).await;

        while let Some(entry) = entries.next_entry().await? {
            let entry_path = entry.path();
            let file_name = entry.file_name();
            let dest_path = to.join(file_name);

            self.update_progress(operation_id, |progress| {
                progress.current_file = Some(entry_path.clone());
                progress.processed_files = processed_entries;
                progress.percentage = (processed_entries as f32 / total_entries as f32) * 100.0;
            }).await;

            if entry_path.is_dir() {
                let sub_paths = Box::pin(self.copy_directory_recursive(operation_id, &entry_path, &dest_path)).await?;
                affected_paths.extend(sub_paths);
            } else {
                let file_paths = self.copy_file(operation_id, &entry_path, &dest_path).await?;
                affected_paths.extend(file_paths);
            }

            processed_entries += 1;
        }

        self.update_progress(operation_id, |progress| {
            progress.processed_files = processed_entries;
            progress.percentage = 100.0;
        }).await;

        Ok(affected_paths)
    }

    /// Collect all paths in a directory (for undo operations)
    fn collect_directory_paths(&self, path: &Path) -> Result<Vec<PathBuf>, UiError> {
        let mut paths = Vec::new();
        
        if path.is_dir() {
            paths.push(path.to_path_buf());
            
            for entry in fs::read_dir(path)? {
                let entry = entry?;
                let entry_path = entry.path();
                
                if entry_path.is_dir() {
                    paths.extend(self.collect_directory_paths(&entry_path)?);
                } else {
                    paths.push(entry_path);
                }
            }
        } else {
            paths.push(path.to_path_buf());
        }
        
        Ok(paths)
    }

    /// Update operation progress
    async fn update_progress<F>(&self, operation_id: Uuid, updater: F)
    where
        F: FnOnce(&mut FileOperationProgress),
    {
        let mut operations = self.active_operations.write().await;
        if let Some(progress) = operations.get_mut(&operation_id) {
            updater(progress);
            self.notify_progress(progress.clone()).await;
        }
    }
}
