//! Code Folding System for Advanced Editor
//!
//! This module provides intelligent code folding capabilities
//! that can collapse and expand code blocks, functions, classes, etc.

use std::collections::{HashMap, BTreeMap};
use std::sync::Arc;
use serde::{Serialize, Deserialize};

use crate::error::UiError;

/// Types of foldable regions
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum FoldType {
    /// Function definition
    Function,
    /// Class definition
    Class,
    /// Struct definition
    Struct,
    /// Enum definition
    Enum,
    /// Implementation block
    Impl,
    /// Trait definition
    Trait,
    /// Module definition
    Module,
    /// Block comment
    Comment,
    /// Code block (braces)
    Block,
    /// Array/object literal
    Array,
    /// Import/use statements
    Imports,
    /// Conditional block (if/else)
    Conditional,
    /// Loop block
    Loop,
    /// Match/switch block
    Match,
    /// Custom region
    Custom,
}

/// Foldable region in the code
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct FoldRegion {
    /// Unique identifier for this fold region
    pub id: String,
    
    /// Type of fold
    pub fold_type: FoldType,
    
    /// Start line (0-based, inclusive)
    pub start_line: usize,
    
    /// End line (0-based, inclusive)
    pub end_line: usize,
    
    /// Start column (0-based)
    pub start_column: usize,
    
    /// End column (0-based)
    pub end_column: usize,
    
    /// Display text when folded (e.g., "{ ... }")
    pub placeholder_text: String,
    
    /// Whether this region is currently folded
    pub is_folded: bool,
    
    /// Whether this region can be folded
    pub is_foldable: bool,
    
    /// Nesting level (0 = top level)
    pub level: usize,
    
    /// Parent fold region ID (if nested)
    pub parent_id: Option<String>,
    
    /// Child fold region IDs
    pub children: Vec<String>,
}

/// Code folding engine
pub struct CodeFoldingEngine {
    /// Fold regions by file path
    fold_regions: HashMap<String, BTreeMap<usize, FoldRegion>>,
    
    /// Folding settings
    settings: FoldingSettings,
}

/// Code folding settings
#[derive(Debug, Clone)]
pub struct FoldingSettings {
    /// Enable folding for functions
    pub fold_functions: bool,
    
    /// Enable folding for classes
    pub fold_classes: bool,
    
    /// Enable folding for comments
    pub fold_comments: bool,
    
    /// Enable folding for imports
    pub fold_imports: bool,
    
    /// Enable folding for blocks
    pub fold_blocks: bool,
    
    /// Minimum lines required to make a region foldable
    pub min_fold_lines: usize,
    
    /// Auto-fold imports on file open
    pub auto_fold_imports: bool,
    
    /// Auto-fold large functions (> threshold lines)
    pub auto_fold_large_functions: bool,
    
    /// Threshold for auto-folding large functions
    pub large_function_threshold: usize,
    
    /// Show fold indicators in gutter
    pub show_fold_indicators: bool,
}

impl Default for FoldingSettings {
    fn default() -> Self {
        Self {
            fold_functions: true,
            fold_classes: true,
            fold_comments: true,
            fold_imports: true,
            fold_blocks: true,
            min_fold_lines: 3,
            auto_fold_imports: false,
            auto_fold_large_functions: false,
            large_function_threshold: 50,
            show_fold_indicators: true,
        }
    }
}

impl CodeFoldingEngine {
    /// Create a new code folding engine
    pub fn new() -> Self {
        Self {
            fold_regions: HashMap::new(),
            settings: FoldingSettings::default(),
        }
    }

    /// Analyze text and detect foldable regions
    pub fn analyze_folds(&mut self, file_path: &str, text: &str, language: &str) -> Result<Vec<FoldRegion>, UiError> {
        let mut regions = Vec::new();
        let lines: Vec<&str> = text.lines().collect();

        match language {
            "rust" => {
                regions.extend(self.analyze_rust_folds(&lines)?);
            }
            "javascript" | "typescript" => {
                regions.extend(self.analyze_js_folds(&lines)?);
            }
            "python" => {
                regions.extend(self.analyze_python_folds(&lines)?);
            }
            _ => {
                // Generic folding based on braces
                regions.extend(self.analyze_generic_folds(&lines)?);
            }
        }

        // Sort regions by start line
        regions.sort_by_key(|r| r.start_line);

        // Build hierarchy
        self.build_fold_hierarchy(&mut regions);

        // Store regions
        let mut region_map = BTreeMap::new();
        for region in &regions {
            region_map.insert(region.start_line, region.clone());
        }
        self.fold_regions.insert(file_path.to_string(), region_map);

        Ok(regions)
    }

    /// Analyze Rust code for foldable regions
    fn analyze_rust_folds(&self, lines: &[&str]) -> Result<Vec<FoldRegion>, UiError> {
        let mut regions = Vec::new();
        let mut brace_stack = Vec::new();
        let mut in_comment = false;
        let mut comment_start = None;

        for (line_idx, line) in lines.iter().enumerate() {
            let trimmed = line.trim();

            // Handle multi-line comments
            if let Some(start_pos) = line.find("/*") {
                if !in_comment {
                    in_comment = true;
                    comment_start = Some((line_idx, start_pos));
                }
            }
            if let Some(end_pos) = line.find("*/") {
                if in_comment {
                    if let Some((start_line, start_col)) = comment_start {
                        if line_idx - start_line >= self.settings.min_fold_lines {
                            regions.push(FoldRegion {
                                id: format!("comment_{}_{}", start_line, start_col),
                                fold_type: FoldType::Comment,
                                start_line,
                                end_line: line_idx,
                                start_column: start_col,
                                end_column: end_pos + 2,
                                placeholder_text: "/* ... */".to_string(),
                                is_folded: false,
                                is_foldable: true,
                                level: 0,
                                parent_id: None,
                                children: Vec::new(),
                            });
                        }
                    }
                    in_comment = false;
                    comment_start = None;
                }
            }

            // Skip if in comment
            if in_comment {
                continue;
            }

            // Handle single-line comments
            if trimmed.starts_with("//") {
                // Look for consecutive comment lines
                let mut end_line = line_idx;
                for (next_idx, next_line) in lines.iter().enumerate().skip(line_idx + 1) {
                    if next_line.trim().starts_with("//") {
                        end_line = next_idx;
                    } else {
                        break;
                    }
                }

                if end_line - line_idx >= self.settings.min_fold_lines {
                    regions.push(FoldRegion {
                        id: format!("comment_{}", line_idx),
                        fold_type: FoldType::Comment,
                        start_line: line_idx,
                        end_line,
                        start_column: line.find("//").unwrap_or(0),
                        end_column: lines[end_line].len(),
                        placeholder_text: "// ...".to_string(),
                        is_folded: false,
                        is_foldable: true,
                        level: 0,
                        parent_id: None,
                        children: Vec::new(),
                    });
                }
            }

            // Handle function definitions
            if trimmed.starts_with("fn ") || trimmed.contains(" fn ") {
                if let Some(brace_pos) = line.find('{') {
                    brace_stack.push((line_idx, brace_pos, FoldType::Function));
                }
            }

            // Handle struct definitions
            if trimmed.starts_with("struct ") || trimmed.contains(" struct ") {
                if let Some(brace_pos) = line.find('{') {
                    brace_stack.push((line_idx, brace_pos, FoldType::Struct));
                }
            }

            // Handle enum definitions
            if trimmed.starts_with("enum ") || trimmed.contains(" enum ") {
                if let Some(brace_pos) = line.find('{') {
                    brace_stack.push((line_idx, brace_pos, FoldType::Enum));
                }
            }

            // Handle impl blocks
            if trimmed.starts_with("impl ") || trimmed.contains(" impl ") {
                if let Some(brace_pos) = line.find('{') {
                    brace_stack.push((line_idx, brace_pos, FoldType::Impl));
                }
            }

            // Handle trait definitions
            if trimmed.starts_with("trait ") || trimmed.contains(" trait ") {
                if let Some(brace_pos) = line.find('{') {
                    brace_stack.push((line_idx, brace_pos, FoldType::Trait));
                }
            }

            // Handle module definitions
            if trimmed.starts_with("mod ") || trimmed.contains(" mod ") {
                if let Some(brace_pos) = line.find('{') {
                    brace_stack.push((line_idx, brace_pos, FoldType::Module));
                }
            }

            // Handle generic blocks
            if let Some(brace_pos) = line.find('{') {
                if !brace_stack.iter().any(|(start_line, _, _)| *start_line == line_idx) {
                    brace_stack.push((line_idx, brace_pos, FoldType::Block));
                }
            }

            // Handle closing braces
            if let Some(close_pos) = line.find('}') {
                if let Some((start_line, start_col, fold_type)) = brace_stack.pop() {
                    if line_idx - start_line >= self.settings.min_fold_lines {
                        let placeholder = match fold_type {
                            FoldType::Function => "fn { ... }",
                            FoldType::Struct => "struct { ... }",
                            FoldType::Enum => "enum { ... }",
                            FoldType::Impl => "impl { ... }",
                            FoldType::Trait => "trait { ... }",
                            FoldType::Module => "mod { ... }",
                            _ => "{ ... }",
                        };

                        regions.push(FoldRegion {
                            id: format!("{:?}_{}_{}", fold_type, start_line, start_col),
                            fold_type,
                            start_line,
                            end_line: line_idx,
                            start_column: start_col,
                            end_column: close_pos + 1,
                            placeholder_text: placeholder.to_string(),
                            is_folded: false,
                            is_foldable: true,
                            level: 0,
                            parent_id: None,
                            children: Vec::new(),
                        });
                    }
                }
            }
        }

        Ok(regions)
    }

    /// Analyze JavaScript/TypeScript code for foldable regions
    fn analyze_js_folds(&self, lines: &[&str]) -> Result<Vec<FoldRegion>, UiError> {
        // Similar to Rust but with JS-specific patterns
        // This is a simplified implementation
        self.analyze_generic_folds(lines)
    }

    /// Analyze Python code for foldable regions
    fn analyze_python_folds(&self, lines: &[&str]) -> Result<Vec<FoldRegion>, UiError> {
        let mut regions = Vec::new();
        let mut indent_stack = Vec::new();

        for (line_idx, line) in lines.iter().enumerate() {
            let trimmed = line.trim();
            if trimmed.is_empty() || trimmed.starts_with('#') {
                continue;
            }

            let indent_level = line.len() - line.trim_start().len();

            // Handle function definitions
            if trimmed.starts_with("def ") || trimmed.starts_with("async def ") {
                indent_stack.push((line_idx, indent_level, FoldType::Function));
            }
            // Handle class definitions
            else if trimmed.starts_with("class ") {
                indent_stack.push((line_idx, indent_level, FoldType::Class));
            }
            // Handle other indented blocks
            else if trimmed.ends_with(':') {
                indent_stack.push((line_idx, indent_level, FoldType::Block));
            }

            // Check for end of indented blocks
            while let Some(&(start_line, start_indent, fold_type)) = indent_stack.last() {
                if indent_level <= start_indent && line_idx > start_line {
                    indent_stack.pop();
                    
                    if line_idx - start_line >= self.settings.min_fold_lines {
                        let placeholder = match fold_type {
                            FoldType::Function => "def ...",
                            FoldType::Class => "class ...",
                            _ => "...",
                        };

                        regions.push(FoldRegion {
                            id: format!("{:?}_{}", fold_type, start_line),
                            fold_type,
                            start_line,
                            end_line: line_idx - 1,
                            start_column: start_indent,
                            end_column: lines[line_idx - 1].len(),
                            placeholder_text: placeholder.to_string(),
                            is_folded: false,
                            is_foldable: true,
                            level: 0,
                            parent_id: None,
                            children: Vec::new(),
                        });
                    }
                } else {
                    break;
                }
            }
        }

        Ok(regions)
    }

    /// Analyze generic code for foldable regions (brace-based)
    fn analyze_generic_folds(&self, lines: &[&str]) -> Result<Vec<FoldRegion>, UiError> {
        let mut regions = Vec::new();
        let mut brace_stack = Vec::new();

        for (line_idx, line) in lines.iter().enumerate() {
            // Handle opening braces
            for (col_idx, ch) in line.char_indices() {
                match ch {
                    '{' => {
                        brace_stack.push((line_idx, col_idx, FoldType::Block));
                    }
                    '}' => {
                        if let Some((start_line, start_col, fold_type)) = brace_stack.pop() {
                            if line_idx - start_line >= self.settings.min_fold_lines {
                                regions.push(FoldRegion {
                                    id: format!("block_{}_{}", start_line, start_col),
                                    fold_type,
                                    start_line,
                                    end_line: line_idx,
                                    start_column: start_col,
                                    end_column: col_idx + 1,
                                    placeholder_text: "{ ... }".to_string(),
                                    is_folded: false,
                                    is_foldable: true,
                                    level: 0,
                                    parent_id: None,
                                    children: Vec::new(),
                                });
                            }
                        }
                    }
                    _ => {}
                }
            }
        }

        Ok(regions)
    }

    /// Build fold hierarchy (parent-child relationships)
    fn build_fold_hierarchy(&self, regions: &mut [FoldRegion]) {
        for i in 0..regions.len() {
            for j in 0..regions.len() {
                if i != j {
                    let (region_a, region_b) = if i < j {
                        let (left, right) = regions.split_at_mut(j);
                        (&mut left[i], &mut right[0])
                    } else {
                        let (left, right) = regions.split_at_mut(i);
                        (&mut right[0], &mut left[j])
                    };

                    // Check if region_b is inside region_a
                    if region_a.start_line < region_b.start_line && region_a.end_line > region_b.end_line {
                        region_b.parent_id = Some(region_a.id.clone());
                        region_a.children.push(region_b.id.clone());
                        region_b.level = region_a.level + 1;
                    }
                }
            }
        }
    }

    /// Toggle fold state for a region
    pub fn toggle_fold(&mut self, file_path: &str, line: usize) -> Result<bool, UiError> {
        if let Some(regions) = self.fold_regions.get_mut(file_path) {
            if let Some(region) = regions.get_mut(&line) {
                region.is_folded = !region.is_folded;
                return Ok(region.is_folded);
            }
        }
        Err(UiError::GenericError("Fold region not found".to_string()))
    }

    /// Fold all regions of a specific type
    pub fn fold_all_type(&mut self, file_path: &str, fold_type: FoldType) -> Result<(), UiError> {
        if let Some(regions) = self.fold_regions.get_mut(file_path) {
            for region in regions.values_mut() {
                if region.fold_type == fold_type {
                    region.is_folded = true;
                }
            }
        }
        Ok(())
    }

    /// Unfold all regions
    pub fn unfold_all(&mut self, file_path: &str) -> Result<(), UiError> {
        if let Some(regions) = self.fold_regions.get_mut(file_path) {
            for region in regions.values_mut() {
                region.is_folded = false;
            }
        }
        Ok(())
    }

    /// Get fold regions for a file
    pub fn get_fold_regions(&self, file_path: &str) -> Vec<FoldRegion> {
        self.fold_regions
            .get(file_path)
            .map(|regions| regions.values().cloned().collect())
            .unwrap_or_default()
    }

    /// Get folded lines for a file
    pub fn get_folded_lines(&self, file_path: &str) -> Vec<(usize, usize)> {
        let mut folded_lines = Vec::new();
        
        if let Some(regions) = self.fold_regions.get(file_path) {
            for region in regions.values() {
                if region.is_folded {
                    folded_lines.push((region.start_line, region.end_line));
                }
            }
        }
        
        folded_lines
    }

    /// Update folding settings
    pub fn update_settings(&mut self, settings: FoldingSettings) {
        self.settings = settings;
    }

    /// Get current settings
    pub fn get_settings(&self) -> &FoldingSettings {
        &self.settings
    }
}
