//! File Operations Engine
//!
//! This module provides a comprehensive file operations system with undo/redo support,
//! progress tracking, and atomic operations for MATRIX IDE.

use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::collections::VecDeque;
use std::time::{SystemTime, UNIX_EPOCH};
use serde::{Serialize, Deserialize};
use tokio::sync::{RwLock, Mutex};
use uuid::Uuid;

pub mod operations;
pub mod undo_redo;
pub mod progress;
pub mod context_menu;
pub mod file_watcher;

use matrix_core::{Engine as CoreEngine, Event};
use crate::error::UiError;
use file_watcher::{FileWatcher, FileWatcherConfig};

/// File operation types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FileOperationType {
    Create { path: PathBuf, is_directory: bool },
    Delete { path: PathBuf },
    Rename { from: PathBuf, to: PathBuf },
    Move { from: PathBuf, to: PathBuf },
    Copy { from: PathBuf, to: PathBuf },
    Duplicate { source: PathBuf, target: PathBuf },
    /// Batch operation containing multiple operations
    Batch { operations: Vec<FileOperationType> },
    /// Atomic operation that must succeed completely or be rolled back
    Atomic { operations: Vec<FileOperationType> },
}

/// File operation result
#[derive(Debug, Clone, Serialize)]
pub struct FileOperationResult {
    pub operation_id: Uuid,
    pub operation_type: FileOperationType,
    pub success: bool,
    pub error: Option<String>,
    pub timestamp: u64,
    pub affected_paths: Vec<PathBuf>,
}

/// File operation progress
#[derive(Debug, Clone)]
pub struct FileOperationProgress {
    pub operation_id: Uuid,
    pub operation_type: FileOperationType,
    pub current_file: Option<PathBuf>,
    pub processed_files: usize,
    pub total_files: usize,
    pub bytes_processed: u64,
    pub total_bytes: u64,
    pub percentage: f32,
    pub is_complete: bool,
    pub error: Option<String>,
}

/// Undo/Redo operation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UndoRedoOperation {
    pub id: Uuid,
    pub operation_type: FileOperationType,
    pub reverse_operation: FileOperationType,
    pub timestamp: u64,
    pub description: String,
}

/// File Operations Engine
pub struct FileOperationsEngine {
    /// Core engine reference
    core: Arc<CoreEngine>,
    
    /// Operation history for undo/redo
    undo_stack: Arc<Mutex<VecDeque<UndoRedoOperation>>>,
    redo_stack: Arc<Mutex<VecDeque<UndoRedoOperation>>>,
    
    /// Active operations with progress tracking
    active_operations: Arc<RwLock<std::collections::HashMap<Uuid, FileOperationProgress>>>,

    /// Cancellation tokens for active operations
    cancellation_tokens: Arc<RwLock<std::collections::HashMap<Uuid, tokio_util::sync::CancellationToken>>>,
    
    /// Maximum undo history size
    max_undo_history: usize,
    
    /// Operation callbacks
    progress_callbacks: Arc<RwLock<Vec<Box<dyn Fn(FileOperationProgress) + Send + Sync>>>>,
    completion_callbacks: Arc<RwLock<Vec<Box<dyn Fn(FileOperationResult) + Send + Sync>>>>,

    /// File watcher for automatic updates
    file_watcher: Option<Arc<FileWatcher>>,
}

impl FileOperationsEngine {
    /// Create a new file operations engine
    pub fn new(core: Arc<CoreEngine>) -> Self {
        Self {
            core,
            undo_stack: Arc::new(Mutex::new(VecDeque::new())),
            redo_stack: Arc::new(Mutex::new(VecDeque::new())),
            active_operations: Arc::new(RwLock::new(std::collections::HashMap::new())),
            cancellation_tokens: Arc::new(RwLock::new(std::collections::HashMap::new())),
            max_undo_history: 100,
            progress_callbacks: Arc::new(RwLock::new(Vec::new())),
            completion_callbacks: Arc::new(RwLock::new(Vec::new())),
            file_watcher: None,
        }
    }

    /// Create a new file operations engine with file watching enabled
    pub fn new_with_watcher(core: Arc<CoreEngine>, watcher_config: FileWatcherConfig) -> Self {
        let file_watcher = Arc::new(FileWatcher::new(core.clone(), watcher_config));

        Self {
            core,
            undo_stack: Arc::new(Mutex::new(VecDeque::new())),
            redo_stack: Arc::new(Mutex::new(VecDeque::new())),
            active_operations: Arc::new(RwLock::new(std::collections::HashMap::new())),
            cancellation_tokens: Arc::new(RwLock::new(std::collections::HashMap::new())),
            max_undo_history: 100,
            progress_callbacks: Arc::new(RwLock::new(Vec::new())),
            completion_callbacks: Arc::new(RwLock::new(Vec::new())),
            file_watcher: Some(file_watcher),
        }
    }

    /// Execute a file operation with progress tracking and undo support
    pub async fn execute_operation(&self, operation: FileOperationType) -> Result<FileOperationResult, UiError> {
        let operation_id = Uuid::new_v4();
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        // Create cancellation token for this operation
        let cancellation_token = tokio_util::sync::CancellationToken::new();
        self.cancellation_tokens.write().await.insert(operation_id, cancellation_token.clone());

        // Initialize progress tracking
        let progress = FileOperationProgress {
            operation_id,
            operation_type: operation.clone(),
            current_file: None,
            processed_files: 0,
            total_files: 1,
            bytes_processed: 0,
            total_bytes: 0,
            percentage: 0.0,
            is_complete: false,
            error: None,
        };

        self.active_operations.write().await.insert(operation_id, progress.clone());
        self.notify_progress(progress.clone()).await;

        // Execute the operation
        let result = match &operation {
            FileOperationType::Create { path, is_directory } => {
                self.create_file_or_directory(operation_id, path, *is_directory).await
            }
            FileOperationType::Delete { path } => {
                self.delete_file_or_directory(operation_id, path).await
            }
            FileOperationType::Rename { from, to } => {
                self.rename_file_or_directory(operation_id, from, to).await
            }
            FileOperationType::Move { from, to } => {
                self.move_file_or_directory(operation_id, from, to).await
            }
            FileOperationType::Copy { from, to } => {
                self.copy_file_or_directory(operation_id, from, to).await
            }
            FileOperationType::Duplicate { source, target } => {
                self.duplicate_file_or_directory(operation_id, source, target).await
            }
            FileOperationType::Batch { operations } => {
                // Execute batch operations and collect all affected paths
                let batch_results = self.execute_batch_operations(operations.clone()).await?;
                let mut all_affected_paths = Vec::new();
                for result in batch_results {
                    all_affected_paths.extend(result.affected_paths);
                }
                Ok(all_affected_paths)
            }
            FileOperationType::Atomic { operations } => {
                // Execute atomic operations and collect all affected paths
                let atomic_results = self.execute_atomic_operations(operations.clone()).await?;
                let mut all_affected_paths = Vec::new();
                for result in atomic_results {
                    all_affected_paths.extend(result.affected_paths);
                }
                Ok(all_affected_paths)
            }
        };

        // Update progress to complete
        let mut final_progress = self.active_operations.read().await
            .get(&operation_id)
            .cloned()
            .unwrap_or(progress);
        
        final_progress.is_complete = true;
        final_progress.percentage = 100.0;
        
        if let Err(ref error) = result {
            final_progress.error = Some(error.to_string());
        }

        self.active_operations.write().await.insert(operation_id, final_progress.clone());
        self.notify_progress(final_progress).await;

        // Create result
        let operation_result = match result {
            Ok(affected_paths) => {
                // Add to undo stack if successful
                if let Some(reverse_op) = self.create_reverse_operation_for_undo(&operation) {
                    let undo_op = UndoRedoOperation {
                        id: operation_id,
                        operation_type: operation.clone(),
                        reverse_operation: reverse_op,
                        timestamp,
                        description: self.operation_description(&operation),
                    };
                    
                    self.add_to_undo_stack(undo_op).await;
                }

                FileOperationResult {
                    operation_id,
                    operation_type: operation.clone(),
                    success: true,
                    affected_paths,
                    error: None,
                    timestamp,
                    affected_paths,
                }
            }
            Err(error) => {
                FileOperationResult {
                    operation_id,
                    operation_type: operation.clone(),
                    success: false,
                    affected_paths: vec![],
                    error: Some(error.to_string()),
                    timestamp,
                }
            }
        };

        // Notify completion
        self.notify_completion(operation_result.clone()).await;

        // Clean up active operations
        self.active_operations.write().await.remove(&operation_id);

        // Emit event to core engine
        let event = Event::Custom {
            source: "file_operations".to_string(),
            name: "operation_completed".to_string(),
            data: serde_json::to_value(&operation_result).unwrap_or_default(),
        };
        let _ = self.core.event_bus().emit(event);

        if operation_result.success {
            Ok(operation_result)
        } else {
            Err(UiError::FileOperation(operation_result.error.unwrap_or_default()))
        }
    }

    /// Get current operation progress
    pub async fn get_operation_progress(&self, operation_id: Uuid) -> Option<FileOperationProgress> {
        self.active_operations.read().await.get(&operation_id).cloned()
    }

    /// Get all active operations
    pub async fn get_active_operations(&self) -> Vec<FileOperationProgress> {
        self.active_operations.read().await.values().cloned().collect()
    }

    /// Cancel an active operation
    pub async fn cancel_operation(&self, operation_id: Uuid) -> Result<(), UiError> {
        // Signal cancellation
        if let Some(token) = self.cancellation_tokens.read().await.get(&operation_id) {
            token.cancel();
        }

        // Update progress to show cancellation
        if let Some(mut progress) = self.active_operations.write().await.get_mut(&operation_id) {
            progress.error = Some("Operation cancelled by user".to_string());
            progress.is_complete = true;
            self.notify_progress(progress.clone()).await;
        }

        // Clean up
        self.active_operations.write().await.remove(&operation_id);
        self.cancellation_tokens.write().await.remove(&operation_id);

        // Emit cancellation event
        let event = Event::Custom {
            source: "file_operations".to_string(),
            name: "operation_cancelled".to_string(),
            data: serde_json::json!({ "operation_id": operation_id }),
        };
        let _ = self.core.event_bus().emit(event);

        Ok(())
    }

    /// Add progress callback
    pub async fn add_progress_callback<F>(&self, callback: F)
    where
        F: Fn(FileOperationProgress) + Send + Sync + 'static,
    {
        self.progress_callbacks.write().await.push(Box::new(callback));
    }

    /// Add completion callback
    pub async fn add_completion_callback<F>(&self, callback: F)
    where
        F: Fn(FileOperationResult) + Send + Sync + 'static,
    {
        self.completion_callbacks.write().await.push(Box::new(callback));
    }

    /// Notify progress callbacks
    async fn notify_progress(&self, progress: FileOperationProgress) {
        let callbacks = self.progress_callbacks.read().await;
        for callback in callbacks.iter() {
            callback(progress.clone());
        }
    }

    /// Notify completion callbacks
    async fn notify_completion(&self, result: FileOperationResult) {
        let callbacks = self.completion_callbacks.read().await;
        for callback in callbacks.iter() {
            callback(result.clone());
        }
    }

    /// Create operation description
    fn operation_description(&self, operation: &FileOperationType) -> String {
        match operation {
            FileOperationType::Create { path, is_directory } => {
                if *is_directory {
                    format!("Create directory: {}", path.display())
                } else {
                    format!("Create file: {}", path.display())
                }
            }
            FileOperationType::Delete { path } => {
                format!("Delete: {}", path.display())
            }
            FileOperationType::Rename { from, to } => {
                format!("Rename: {} → {}", from.display(), to.display())
            }
            FileOperationType::Move { from, to } => {
                format!("Move: {} → {}", from.display(), to.display())
            }
            FileOperationType::Copy { from, to } => {
                format!("Copy: {} → {}", from.display(), to.display())
            }
            FileOperationType::Duplicate { source, target } => {
                format!("Duplicate: {} → {}", source.display(), target.display())
            }
        }
    }

    /// Execute batch operations (non-atomic, continues on failure)
    pub async fn execute_batch_operations(&self, operations: Vec<FileOperationType>) -> Result<Vec<FileOperationResult>, UiError> {
        let mut results = Vec::new();
        let total_operations = operations.len();

        for (index, operation) in operations.into_iter().enumerate() {
            // Update overall progress
            let overall_progress = (index as f32 / total_operations as f32) * 100.0;

            match self.execute_operation(operation).await {
                Ok(result) => results.push(result),
                Err(e) => {
                    // For batch operations, we continue even if one fails
                    let error_result = FileOperationResult {
                        operation_id: Uuid::new_v4(),
                        success: false,
                        affected_paths: Vec::new(),
                        error: Some(e.to_string()),
                        timestamp: SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_secs(),
                    };
                    results.push(error_result);
                }
            }
        }

        Ok(results)
    }

    /// Execute atomic operations (all succeed or all are rolled back)
    pub async fn execute_atomic_operations(&self, operations: Vec<FileOperationType>) -> Result<Vec<FileOperationResult>, UiError> {
        let mut results = Vec::new();
        let mut completed_operations = Vec::new();

        // Execute all operations
        for operation in operations {
            match self.execute_operation(operation.clone()).await {
                Ok(result) => {
                    results.push(result.clone());
                    completed_operations.push((operation, result));
                }
                Err(e) => {
                    // Rollback all completed operations
                    for (completed_op, _) in completed_operations.into_iter().rev() {
                        if let Ok(undo_op) = self.create_reverse_operation(&completed_op) {
                            let _ = self.execute_operation(undo_op).await;
                        }
                    }
                    return Err(e);
                }
            }
        }

        Ok(results)
    }

    /// Create reverse operation for rollback
    fn create_reverse_operation(&self, operation: &FileOperationType) -> Result<FileOperationType, UiError> {
        match operation {
            FileOperationType::Create { path, is_directory: _ } => {
                Ok(FileOperationType::Delete { path: path.clone() })
            }
            FileOperationType::Delete { path } => {
                // Note: This is a simplified reverse - in practice, you'd need to store the original content
                Err(UiError::FileOperation("Cannot reverse delete operation without backup".to_string()))
            }
            FileOperationType::Copy { from: _, to } => {
                Ok(FileOperationType::Delete { path: to.clone() })
            }
            FileOperationType::Move { from, to } => {
                Ok(FileOperationType::Move { from: to.clone(), to: from.clone() })
            }
            FileOperationType::Rename { from, to } => {
                Ok(FileOperationType::Rename { from: to.clone(), to: from.clone() })
            }
            FileOperationType::Duplicate { source: _, target } => {
                Ok(FileOperationType::Delete { path: target.clone() })
            }
            FileOperationType::Batch { operations: _ } => {
                Err(UiError::FileOperation("Cannot create reverse operation for batch operations".to_string()))
            }
            FileOperationType::Atomic { operations: _ } => {
                Err(UiError::FileOperation("Cannot create reverse operation for atomic operations".to_string()))
            }
        }
    }

    /// Check if operation can be cancelled
    pub async fn can_cancel_operation(&self, operation_id: Uuid) -> bool {
        self.cancellation_tokens.read().await.contains_key(&operation_id)
    }

    /// Get cancellation token for operation (for internal use)
    pub async fn get_cancellation_token(&self, operation_id: Uuid) -> Option<tokio_util::sync::CancellationToken> {
        self.cancellation_tokens.read().await.get(&operation_id).cloned()
    }

    /// Start watching a directory for changes
    pub async fn start_watching_directory(&self, path: &Path) -> Result<Uuid, UiError> {
        if let Some(watcher) = &self.file_watcher {
            watcher.watch_directory(path).await
        } else {
            Err(UiError::FileOperation("File watcher not enabled".to_string()))
        }
    }

    /// Stop watching a directory
    pub async fn stop_watching_directory(&self, path: &Path) -> Result<(), UiError> {
        if let Some(watcher) = &self.file_watcher {
            watcher.stop_watching(path).await
        } else {
            Err(UiError::FileOperation("File watcher not enabled".to_string()))
        }
    }

    /// Get all watched directories
    pub async fn get_watched_directories(&self) -> Vec<PathBuf> {
        if let Some(watcher) = &self.file_watcher {
            watcher.get_watched_paths().await
        } else {
            Vec::new()
        }
    }

    /// Check if file watching is enabled
    pub fn is_file_watching_enabled(&self) -> bool {
        self.file_watcher.is_some()
    }
}
