//! Advanced Search and Replace System for Editor
//!
//! This module provides comprehensive search and replace functionality
//! with support for regex, case sensitivity, whole words, and more.

use std::collections::HashMap;
use std::path::PathBuf;
use regex::{Regex, RegexBuilder};
use serde::{Serialize, Deserialize};

use crate::error::UiError;

/// Search options
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchOptions {
    /// Case sensitive search
    pub case_sensitive: bool,
    
    /// Match whole words only
    pub whole_words: bool,
    
    /// Use regular expressions
    pub use_regex: bool,
    
    /// Search in selection only
    pub in_selection: bool,
    
    /// Search backwards
    pub backwards: bool,
    
    /// Wrap around when reaching end/beginning
    pub wrap_around: bool,
    
    /// Include hidden files in file search
    pub include_hidden: bool,
    
    /// File patterns to include (glob patterns)
    pub include_patterns: Vec<String>,
    
    /// File patterns to exclude (glob patterns)
    pub exclude_patterns: Vec<String>,
    
    /// Maximum number of results to return
    pub max_results: usize,
}

impl Default for SearchOptions {
    fn default() -> Self {
        Self {
            case_sensitive: false,
            whole_words: false,
            use_regex: false,
            in_selection: false,
            backwards: false,
            wrap_around: true,
            include_hidden: false,
            include_patterns: vec!["*".to_string()],
            exclude_patterns: vec![
                "*.git/*".to_string(),
                "*/node_modules/*".to_string(),
                "*/target/*".to_string(),
                "*.tmp".to_string(),
                "*.log".to_string(),
            ],
            max_results: 1000,
        }
    }
}

/// Search result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    /// File path
    pub file_path: PathBuf,
    
    /// Line number (0-based)
    pub line: usize,
    
    /// Column number (0-based)
    pub column: usize,
    
    /// End column (0-based)
    pub end_column: usize,
    
    /// Matched text
    pub matched_text: String,
    
    /// Line content
    pub line_content: String,
    
    /// Context lines before match
    pub context_before: Vec<String>,
    
    /// Context lines after match
    pub context_after: Vec<String>,
}

/// Replace operation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ReplaceOperation {
    /// Search result to replace
    pub result: SearchResult,
    
    /// Replacement text
    pub replacement: String,
    
    /// Whether this replacement was applied
    pub applied: bool,
}

/// Search and replace engine
pub struct SearchReplaceEngine {
    /// Current search query
    current_query: Option<String>,
    
    /// Current search options
    current_options: SearchOptions,
    
    /// Compiled regex for current search
    compiled_regex: Option<Regex>,
    
    /// Search results cache
    results_cache: HashMap<String, Vec<SearchResult>>,
    
    /// Replace operations history
    replace_history: Vec<ReplaceOperation>,
}

impl SearchReplaceEngine {
    /// Create a new search and replace engine
    pub fn new() -> Self {
        Self {
            current_query: None,
            current_options: SearchOptions::default(),
            compiled_regex: None,
            results_cache: HashMap::new(),
            replace_history: Vec::new(),
        }
    }

    /// Search in text content
    pub fn search_in_text(
        &mut self,
        query: &str,
        text: &str,
        options: SearchOptions,
    ) -> Result<Vec<SearchResult>, UiError> {
        self.current_query = Some(query.to_string());
        self.current_options = options.clone();
        
        // Compile regex if needed
        if options.use_regex {
            let regex_builder = RegexBuilder::new(query)
                .case_insensitive(!options.case_sensitive)
                .multi_line(true);
            
            self.compiled_regex = Some(regex_builder.build()
                .map_err(|e| UiError::GenericError(format!("Invalid regex: {}", e)))?);
        } else {
            self.compiled_regex = None;
        }

        let mut results = Vec::new();
        let lines: Vec<&str> = text.lines().collect();

        for (line_idx, line) in lines.iter().enumerate() {
            let matches = self.find_matches_in_line(query, line, &options)?;
            
            for (start_col, end_col, matched_text) in matches {
                // Get context lines
                let context_before = self.get_context_lines(&lines, line_idx, 3, true);
                let context_after = self.get_context_lines(&lines, line_idx, 3, false);

                results.push(SearchResult {
                    file_path: PathBuf::from("<current>"),
                    line: line_idx,
                    column: start_col,
                    end_column: end_col,
                    matched_text,
                    line_content: line.to_string(),
                    context_before,
                    context_after,
                });

                if results.len() >= options.max_results {
                    break;
                }
            }

            if results.len() >= options.max_results {
                break;
            }
        }

        Ok(results)
    }

    /// Search in multiple files
    pub async fn search_in_files(
        &mut self,
        query: &str,
        root_path: &std::path::Path,
        options: SearchOptions,
    ) -> Result<Vec<SearchResult>, UiError> {
        self.current_query = Some(query.to_string());
        self.current_options = options.clone();

        let mut results = Vec::new();
        let files = self.collect_files(root_path, &options).await?;

        for file_path in files {
            if results.len() >= options.max_results {
                break;
            }

            match tokio::fs::read_to_string(&file_path).await {
                Ok(content) => {
                    let file_results = self.search_in_file_content(query, &file_path, &content, &options)?;
                    results.extend(file_results);
                }
                Err(_) => {
                    // Skip files that can't be read
                    continue;
                }
            }
        }

        // Cache results
        let cache_key = format!("{}:{:?}", query, options.case_sensitive);
        self.results_cache.insert(cache_key, results.clone());

        Ok(results)
    }

    /// Find matches in a single line
    fn find_matches_in_line(
        &self,
        query: &str,
        line: &str,
        options: &SearchOptions,
    ) -> Result<Vec<(usize, usize, String)>, UiError> {
        let mut matches = Vec::new();

        if options.use_regex {
            if let Some(regex) = &self.compiled_regex {
                for mat in regex.find_iter(line) {
                    matches.push((mat.start(), mat.end(), mat.as_str().to_string()));
                }
            }
        } else {
            let search_line = if options.case_sensitive { line } else { &line.to_lowercase() };
            let search_query = if options.case_sensitive { query } else { &query.to_lowercase() };

            let mut start = 0;
            while let Some(pos) = search_line[start..].find(search_query) {
                let actual_pos = start + pos;
                let end_pos = actual_pos + query.len();

                // Check whole word constraint
                if options.whole_words {
                    let before_ok = actual_pos == 0 || 
                        !line.chars().nth(actual_pos - 1).unwrap_or(' ').is_alphanumeric();
                    let after_ok = end_pos >= line.len() || 
                        !line.chars().nth(end_pos).unwrap_or(' ').is_alphanumeric();
                    
                    if !before_ok || !after_ok {
                        start = actual_pos + 1;
                        continue;
                    }
                }

                matches.push((actual_pos, end_pos, line[actual_pos..end_pos].to_string()));
                start = actual_pos + 1;
            }
        }

        Ok(matches)
    }

    /// Search in file content
    fn search_in_file_content(
        &mut self,
        query: &str,
        file_path: &std::path::Path,
        content: &str,
        options: &SearchOptions,
    ) -> Result<Vec<SearchResult>, UiError> {
        let mut results = Vec::new();
        let lines: Vec<&str> = content.lines().collect();

        for (line_idx, line) in lines.iter().enumerate() {
            let matches = self.find_matches_in_line(query, line, options)?;
            
            for (start_col, end_col, matched_text) in matches {
                let context_before = self.get_context_lines(&lines, line_idx, 2, true);
                let context_after = self.get_context_lines(&lines, line_idx, 2, false);

                results.push(SearchResult {
                    file_path: file_path.to_path_buf(),
                    line: line_idx,
                    column: start_col,
                    end_column: end_col,
                    matched_text,
                    line_content: line.to_string(),
                    context_before,
                    context_after,
                });
            }
        }

        Ok(results)
    }

    /// Get context lines around a match
    fn get_context_lines(
        &self,
        lines: &[&str],
        center_line: usize,
        context_size: usize,
        before: bool,
    ) -> Vec<String> {
        let mut context = Vec::new();

        if before {
            let start = center_line.saturating_sub(context_size);
            for i in start..center_line {
                if i < lines.len() {
                    context.push(lines[i].to_string());
                }
            }
        } else {
            let end = std::cmp::min(center_line + context_size + 1, lines.len());
            for i in (center_line + 1)..end {
                context.push(lines[i].to_string());
            }
        }

        context
    }

    /// Collect files to search in
    fn collect_files<'a>(
        &'a self,
        root_path: &'a std::path::Path,
        options: &'a SearchOptions,
    ) -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<Vec<PathBuf>, UiError>> + 'a>> {
        Box::pin(async move {
            let mut files = Vec::new();
            let mut entries = tokio::fs::read_dir(root_path).await
                .map_err(|e| UiError::IoError(e.to_string()))?;

            while let Ok(Some(entry)) = entries.next_entry().await {
                let path = entry.path();

                if path.is_file() {
                    // Check if file matches include/exclude patterns
                    if self.should_include_file(&path, options) {
                        files.push(path);
                    }
                } else if path.is_dir() {
                    // Recursively search subdirectories
                    let subfiles = self.collect_files(&path, options).await?;
                    files.extend(subfiles);
                }
            }

            Ok(files)
        })
    }

    /// Check if file should be included in search
    fn should_include_file(&self, path: &std::path::Path, options: &SearchOptions) -> bool {
        let path_str = path.to_string_lossy();

        // Check hidden files
        if !options.include_hidden {
            if let Some(file_name) = path.file_name() {
                if file_name.to_string_lossy().starts_with('.') {
                    return false;
                }
            }
        }

        // Check exclude patterns
        for pattern in &options.exclude_patterns {
            if glob::Pattern::new(pattern).map_or(false, |p| p.matches(&path_str)) {
                return false;
            }
        }

        // Check include patterns
        for pattern in &options.include_patterns {
            if glob::Pattern::new(pattern).map_or(false, |p| p.matches(&path_str)) {
                return true;
            }
        }

        false
    }

    /// Replace text in content
    pub fn replace_in_text(
        &mut self,
        content: &str,
        replacement: &str,
        results: &[SearchResult],
    ) -> Result<String, UiError> {
        let mut new_content = content.to_string();
        let mut offset = 0i32;

        // Sort results by position (reverse order for correct offset handling)
        let mut sorted_results = results.to_vec();
        sorted_results.sort_by(|a, b| {
            b.line.cmp(&a.line).then_with(|| b.column.cmp(&a.column))
        });

        for result in sorted_results {
            let lines: Vec<&str> = new_content.lines().collect();
            if result.line < lines.len() {
                let line = lines[result.line];
                let before = &line[..result.column];
                let after = &line[result.end_column..];
                
                let new_line = format!("{}{}{}", before, replacement, after);
                
                // Replace the line in content
                let mut new_lines = lines.iter().map(|s| s.to_string()).collect::<Vec<_>>();
                new_lines[result.line] = new_line;
                new_content = new_lines.join("\n");

                // Track replace operation
                self.replace_history.push(ReplaceOperation {
                    result: result.clone(),
                    replacement: replacement.to_string(),
                    applied: true,
                });
            }
        }

        Ok(new_content)
    }

    /// Replace all occurrences
    pub fn replace_all(
        &mut self,
        query: &str,
        replacement: &str,
        content: &str,
        options: SearchOptions,
    ) -> Result<String, UiError> {
        let results = self.search_in_text(query, content, options)?;
        self.replace_in_text(content, replacement, &results)
    }

    /// Get next search result
    pub fn find_next(&self, current_line: usize, current_column: usize) -> Option<&SearchResult> {
        // This would be implemented to find the next result after current position
        // For now, return None
        None
    }

    /// Get previous search result
    pub fn find_previous(&self, current_line: usize, current_column: usize) -> Option<&SearchResult> {
        // This would be implemented to find the previous result before current position
        // For now, return None
        None
    }

    /// Clear search results
    pub fn clear_results(&mut self) {
        self.results_cache.clear();
        self.current_query = None;
        self.compiled_regex = None;
    }

    /// Get current search query
    pub fn get_current_query(&self) -> Option<&String> {
        self.current_query.as_ref()
    }

    /// Get current search options
    pub fn get_current_options(&self) -> &SearchOptions {
        &self.current_options
    }

    /// Get replace history
    pub fn get_replace_history(&self) -> &[ReplaceOperation] {
        &self.replace_history
    }

    /// Clear replace history
    pub fn clear_replace_history(&mut self) {
        self.replace_history.clear();
    }
}
