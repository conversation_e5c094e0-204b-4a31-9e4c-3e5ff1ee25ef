//! LSP Integration for Advanced Editor
//!
//! This module provides comprehensive Language Server Protocol integration
//! for the MATRIX IDE advanced editor.

use std::collections::HashMap;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use tokio::sync::{RwLock, mpsc};
use serde::{Serialize, Deserialize};
use uuid::Uuid;

use matrix_core::Engine as CoreEngine;
use crate::error::UiError;

/// LSP server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LspServerConfig {
    /// Language identifier (e.g., "rust", "typescript", "python")
    pub language: String,
    /// Server command to execute
    pub command: String,
    /// Command arguments
    pub args: Vec<String>,
    /// Working directory for the server
    pub working_directory: Option<PathBuf>,
    /// Environment variables
    pub env: HashMap<String, String>,
    /// File extensions handled by this server
    pub file_extensions: Vec<String>,
    /// Initialization options
    pub init_options: serde_json::Value,
}

/// LSP diagnostic severity levels
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, <PERSON>ialEq, Eq, Serialize, Deserialize)]
pub enum DiagnosticSeverity {
    Error = 1,
    Warning = 2,
    Information = 3,
    Hint = 4,
}

/// LSP diagnostic information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Diagnostic {
    /// File path
    pub file_path: PathBuf,
    /// Line number (0-based)
    pub line: usize,
    /// Column number (0-based)
    pub column: usize,
    /// End line (0-based)
    pub end_line: usize,
    /// End column (0-based)
    pub end_column: usize,
    /// Diagnostic severity
    pub severity: DiagnosticSeverity,
    /// Diagnostic message
    pub message: String,
    /// Diagnostic source (e.g., "rustc", "eslint")
    pub source: Option<String>,
    /// Diagnostic code
    pub code: Option<String>,
}

/// Completion item kind
#[derive(Debug, Clone, Copy, PartialEq, Eq, Serialize, Deserialize)]
pub enum CompletionItemKind {
    Text = 1,
    Method = 2,
    Function = 3,
    Constructor = 4,
    Field = 5,
    Variable = 6,
    Class = 7,
    Interface = 8,
    Module = 9,
    Property = 10,
    Unit = 11,
    Value = 12,
    Enum = 13,
    Keyword = 14,
    Snippet = 15,
    Color = 16,
    File = 17,
    Reference = 18,
    Folder = 19,
    EnumMember = 20,
    Constant = 21,
    Struct = 22,
    Event = 23,
    Operator = 24,
    TypeParameter = 25,
}

/// Completion item
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CompletionItem {
    /// Label shown in completion list
    pub label: String,
    /// Kind of completion item
    pub kind: Option<CompletionItemKind>,
    /// Detail information
    pub detail: Option<String>,
    /// Documentation
    pub documentation: Option<String>,
    /// Text to insert
    pub insert_text: Option<String>,
    /// Sort text for ordering
    pub sort_text: Option<String>,
    /// Filter text for filtering
    pub filter_text: Option<String>,
}

/// Hover information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HoverInfo {
    /// File path
    pub file_path: PathBuf,
    /// Line number (0-based)
    pub line: usize,
    /// Column number (0-based)
    pub column: usize,
    /// Hover contents
    pub contents: Vec<String>,
}

/// Symbol information
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SymbolInfo {
    /// Symbol name
    pub name: String,
    /// Symbol kind
    pub kind: CompletionItemKind,
    /// File path
    pub file_path: PathBuf,
    /// Line number (0-based)
    pub line: usize,
    /// Column number (0-based)
    pub column: usize,
    /// Container name (e.g., class or namespace)
    pub container_name: Option<String>,
}

/// LSP client for managing language servers
pub struct LspClient {
    /// Core engine reference
    core: Arc<CoreEngine>,
    
    /// Active LSP servers by language
    servers: Arc<RwLock<HashMap<String, LspServer>>>,
    
    /// Server configurations
    configs: Arc<RwLock<HashMap<String, LspServerConfig>>>,
    
    /// Current diagnostics by file
    diagnostics: Arc<RwLock<HashMap<PathBuf, Vec<Diagnostic>>>>,
    
    /// Event sender for LSP events
    event_sender: mpsc::UnboundedSender<LspEvent>,
    
    /// Request ID counter
    request_id: Arc<RwLock<u64>>,
}

/// LSP server instance
struct LspServer {
    /// Server process handle
    process: tokio::process::Child,
    
    /// Server configuration
    config: LspServerConfig,
    
    /// Server capabilities
    capabilities: Option<serde_json::Value>,
    
    /// Pending requests
    pending_requests: HashMap<u64, tokio::sync::oneshot::Sender<serde_json::Value>>,
}

/// LSP events
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LspEvent {
    /// Server started
    ServerStarted { language: String },
    
    /// Server stopped
    ServerStopped { language: String },
    
    /// Diagnostics updated
    DiagnosticsUpdated { file_path: PathBuf, diagnostics: Vec<Diagnostic> },
    
    /// Completion available
    CompletionAvailable { file_path: PathBuf, line: usize, column: usize, items: Vec<CompletionItem> },
    
    /// Hover information available
    HoverAvailable { hover_info: HoverInfo },
    
    /// Symbols found
    SymbolsFound { symbols: Vec<SymbolInfo> },
}

impl LspClient {
    /// Create a new LSP client
    pub fn new(core: Arc<CoreEngine>) -> Self {
        let (event_sender, mut event_receiver) = mpsc::unbounded_channel();
        
        // Start event processing task
        let core_clone = core.clone();
        tokio::spawn(async move {
            while let Some(event) = event_receiver.recv().await {
                // Emit LSP events to core engine
                let core_event = matrix_core::Event::Custom {
                    source: "lsp_client".to_string(),
                    name: "lsp_event".to_string(),
                    data: serde_json::to_value(&event).unwrap_or_default(),
                };
                let _ = core_clone.event_bus().emit(core_event);
            }
        });
        
        Self {
            core,
            servers: Arc::new(RwLock::new(HashMap::new())),
            configs: Arc::new(RwLock::new(HashMap::new())),
            diagnostics: Arc::new(RwLock::new(HashMap::new())),
            event_sender,
            request_id: Arc::new(RwLock::new(0)),
        }
    }

    /// Register an LSP server configuration
    pub async fn register_server(&self, config: LspServerConfig) -> Result<(), UiError> {
        let language = config.language.clone();
        self.configs.write().await.insert(language, config);
        Ok(())
    }

    /// Start LSP server for a language
    pub async fn start_server(&self, language: &str) -> Result<(), UiError> {
        let config = self.configs.read().await.get(language).cloned()
            .ok_or_else(|| UiError::LspError(format!("No configuration for language: {}", language)))?;

        // Start server process
        let mut cmd = tokio::process::Command::new(&config.command);
        cmd.args(&config.args);
        
        if let Some(working_dir) = &config.working_directory {
            cmd.current_dir(working_dir);
        }
        
        for (key, value) in &config.env {
            cmd.env(key, value);
        }
        
        cmd.stdin(std::process::Stdio::piped())
           .stdout(std::process::Stdio::piped())
           .stderr(std::process::Stdio::piped());

        let process = cmd.spawn()
            .map_err(|e| UiError::LspError(format!("Failed to start LSP server: {}", e)))?;

        let server = LspServer {
            process,
            config: config.clone(),
            capabilities: None,
            pending_requests: HashMap::new(),
        };

        self.servers.write().await.insert(language.to_string(), server);
        
        // Send server started event
        let _ = self.event_sender.send(LspEvent::ServerStarted {
            language: language.to_string(),
        });

        Ok(())
    }

    /// Stop LSP server for a language
    pub async fn stop_server(&self, language: &str) -> Result<(), UiError> {
        if let Some(mut server) = self.servers.write().await.remove(language) {
            let _ = server.process.kill().await;
            
            // Send server stopped event
            let _ = self.event_sender.send(LspEvent::ServerStopped {
                language: language.to_string(),
            });
        }
        
        Ok(())
    }

    /// Get completion items for a position
    pub async fn get_completion(&self, file_path: &Path, line: usize, column: usize) -> Result<Vec<CompletionItem>, UiError> {
        let language = self.detect_language(file_path)?;
        
        // For now, return mock completion items
        // In a real implementation, this would send a completion request to the LSP server
        let items = vec![
            CompletionItem {
                label: "println!".to_string(),
                kind: Some(CompletionItemKind::Function),
                detail: Some("macro".to_string()),
                documentation: Some("Prints to stdout".to_string()),
                insert_text: Some("println!(\"{}\", )".to_string()),
                sort_text: None,
                filter_text: None,
            },
            CompletionItem {
                label: "String".to_string(),
                kind: Some(CompletionItemKind::Struct),
                detail: Some("std::string::String".to_string()),
                documentation: Some("UTF-8 encoded string".to_string()),
                insert_text: Some("String".to_string()),
                sort_text: None,
                filter_text: None,
            },
        ];
        
        // Send completion available event
        let _ = self.event_sender.send(LspEvent::CompletionAvailable {
            file_path: file_path.to_path_buf(),
            line,
            column,
            items: items.clone(),
        });
        
        Ok(items)
    }

    /// Get hover information for a position
    pub async fn get_hover(&self, file_path: &Path, line: usize, column: usize) -> Result<Option<HoverInfo>, UiError> {
        let _language = self.detect_language(file_path)?;
        
        // Mock hover information
        let hover_info = HoverInfo {
            file_path: file_path.to_path_buf(),
            line,
            column,
            contents: vec![
                "```rust".to_string(),
                "fn example() -> String".to_string(),
                "```".to_string(),
                "Example function documentation".to_string(),
            ],
        };
        
        // Send hover available event
        let _ = self.event_sender.send(LspEvent::HoverAvailable {
            hover_info: hover_info.clone(),
        });
        
        Ok(Some(hover_info))
    }

    /// Get diagnostics for a file
    pub async fn get_diagnostics(&self, file_path: &Path) -> Vec<Diagnostic> {
        self.diagnostics.read().await.get(file_path).cloned().unwrap_or_default()
    }

    /// Update diagnostics for a file
    pub async fn update_diagnostics(&self, file_path: PathBuf, diagnostics: Vec<Diagnostic>) {
        self.diagnostics.write().await.insert(file_path.clone(), diagnostics.clone());
        
        // Send diagnostics updated event
        let _ = self.event_sender.send(LspEvent::DiagnosticsUpdated {
            file_path,
            diagnostics,
        });
    }

    /// Detect language from file extension
    fn detect_language(&self, file_path: &Path) -> Result<String, UiError> {
        let extension = file_path.extension()
            .and_then(|ext| ext.to_str())
            .ok_or_else(|| UiError::LspError("Cannot detect file language".to_string()))?;

        let language = match extension {
            "rs" => "rust",
            "js" | "jsx" => "javascript",
            "ts" | "tsx" => "typescript",
            "py" => "python",
            "go" => "go",
            "java" => "java",
            "cpp" | "cc" | "cxx" => "cpp",
            "c" => "c",
            "cs" => "csharp",
            _ => return Err(UiError::LspError(format!("Unsupported language: {}", extension))),
        };

        Ok(language.to_string())
    }

    /// Get next request ID
    async fn next_request_id(&self) -> u64 {
        let mut id = self.request_id.write().await;
        *id += 1;
        *id
    }
}
