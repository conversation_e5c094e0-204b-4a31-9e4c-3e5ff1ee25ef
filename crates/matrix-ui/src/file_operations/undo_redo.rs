//! Undo/Redo System for File Operations
//!
//! This module provides comprehensive undo/redo functionality for all file operations
//! with proper state management and operation reversal.

use std::path::PathBuf;
use uuid::Uuid;

use crate::error::UiError;
use super::{FileOperationsEngine, FileOperationType, UndoRedoOperation};

impl FileOperationsEngine {
    /// Undo the last operation
    pub async fn undo(&self) -> Result<(), UiError> {
        let operation = {
            let mut undo_stack = self.undo_stack.lock().await;
            undo_stack.pop_back()
        };

        if let Some(operation) = operation {
            // Execute the reverse operation
            match self.execute_operation(operation.reverse_operation.clone()).await {
                Ok(_) => {
                    // Move to redo stack
                    let mut redo_stack = self.redo_stack.lock().await;
                    redo_stack.push_back(operation);
                    
                    // Limit redo stack size
                    while redo_stack.len() > self.max_undo_history {
                        redo_stack.pop_front();
                    }
                    
                    Ok(())
                }
                Err(e) => {
                    // Put the operation back on undo stack if reversal failed
                    let mut undo_stack = self.undo_stack.lock().await;
                    undo_stack.push_back(operation);
                    Err(e)
                }
            }
        } else {
            Err(UiError::FileOperation("Nothing to undo".to_string()))
        }
    }

    /// Redo the last undone operation
    pub async fn redo(&self) -> Result<(), UiError> {
        let operation = {
            let mut redo_stack = self.redo_stack.lock().await;
            redo_stack.pop_back()
        };

        if let Some(operation) = operation {
            // Execute the original operation
            match self.execute_operation(operation.operation_type.clone()).await {
                Ok(_) => {
                    // Move back to undo stack
                    let mut undo_stack = self.undo_stack.lock().await;
                    undo_stack.push_back(operation);
                    Ok(())
                }
                Err(e) => {
                    // Put the operation back on redo stack if execution failed
                    let mut redo_stack = self.redo_stack.lock().await;
                    redo_stack.push_back(operation);
                    Err(e)
                }
            }
        } else {
            Err(UiError::FileOperation("Nothing to redo".to_string()))
        }
    }

    /// Check if undo is available
    pub async fn can_undo(&self) -> bool {
        !self.undo_stack.lock().await.is_empty()
    }

    /// Check if redo is available
    pub async fn can_redo(&self) -> bool {
        !self.redo_stack.lock().await.is_empty()
    }

    /// Get undo history
    pub async fn get_undo_history(&self) -> Vec<UndoRedoOperation> {
        self.undo_stack.lock().await.iter().cloned().collect()
    }

    /// Get redo history
    pub async fn get_redo_history(&self) -> Vec<UndoRedoOperation> {
        self.redo_stack.lock().await.iter().cloned().collect()
    }

    /// Clear undo/redo history
    pub async fn clear_history(&self) {
        self.undo_stack.lock().await.clear();
        self.redo_stack.lock().await.clear();
    }

    /// Add operation to undo stack
    pub(super) async fn add_to_undo_stack(&self, operation: UndoRedoOperation) {
        let mut undo_stack = self.undo_stack.lock().await;
        undo_stack.push_back(operation);
        
        // Limit undo stack size
        while undo_stack.len() > self.max_undo_history {
            undo_stack.pop_front();
        }
        
        // Clear redo stack when new operation is added
        self.redo_stack.lock().await.clear();
    }

    /// Create reverse operation for undo functionality
    pub(super) fn create_reverse_operation(&self, operation: &FileOperationType) -> Option<FileOperationType> {
        match operation {
            FileOperationType::Create { path, is_directory: _ } => {
                Some(FileOperationType::Delete { path: path.clone() })
            }
            FileOperationType::Delete { path } => {
                // Note: This is a simplified reverse operation
                // In a real implementation, we would need to store the deleted content
                // For now, we can't truly reverse a delete operation
                None
            }
            FileOperationType::Rename { from, to } => {
                Some(FileOperationType::Rename { 
                    from: to.clone(), 
                    to: from.clone() 
                })
            }
            FileOperationType::Move { from, to } => {
                Some(FileOperationType::Move { 
                    from: to.clone(), 
                    to: from.clone() 
                })
            }
            FileOperationType::Copy { from: _, to } => {
                Some(FileOperationType::Delete { path: to.clone() })
            }
            FileOperationType::Duplicate { source: _, target } => {
                Some(FileOperationType::Delete { path: target.clone() })
            }
        }
    }

    /// Get the next undo operation description without executing it
    pub async fn get_next_undo_description(&self) -> Option<String> {
        self.undo_stack.lock().await.back().map(|op| op.description.clone())
    }

    /// Get the next redo operation description without executing it
    pub async fn get_next_redo_description(&self) -> Option<String> {
        self.redo_stack.lock().await.back().map(|op| op.description.clone())
    }

    /// Undo multiple operations
    pub async fn undo_multiple(&self, count: usize) -> Result<usize, UiError> {
        let mut successful_undos = 0;
        
        for _ in 0..count {
            match self.undo().await {
                Ok(_) => successful_undos += 1,
                Err(_) => break,
            }
        }
        
        if successful_undos > 0 {
            Ok(successful_undos)
        } else {
            Err(UiError::FileOperation("No operations could be undone".to_string()))
        }
    }

    /// Redo multiple operations
    pub async fn redo_multiple(&self, count: usize) -> Result<usize, UiError> {
        let mut successful_redos = 0;
        
        for _ in 0..count {
            match self.redo().await {
                Ok(_) => successful_redos += 1,
                Err(_) => break,
            }
        }
        
        if successful_redos > 0 {
            Ok(successful_redos)
        } else {
            Err(UiError::FileOperation("No operations could be redone".to_string()))
        }
    }

    /// Undo to a specific operation
    pub async fn undo_to_operation(&self, operation_id: Uuid) -> Result<usize, UiError> {
        let mut undos_performed = 0;
        
        loop {
            let next_operation = {
                let undo_stack = self.undo_stack.lock().await;
                undo_stack.back().cloned()
            };
            
            match next_operation {
                Some(operation) if operation.id == operation_id => {
                    // Found the target operation, undo it and stop
                    self.undo().await?;
                    undos_performed += 1;
                    break;
                }
                Some(_) => {
                    // Not the target operation, undo and continue
                    self.undo().await?;
                    undos_performed += 1;
                }
                None => {
                    return Err(UiError::FileOperation("Target operation not found in undo history".to_string()));
                }
            }
        }
        
        Ok(undos_performed)
    }

    /// Get operation by ID from history
    pub async fn get_operation_by_id(&self, operation_id: Uuid) -> Option<UndoRedoOperation> {
        // Check undo stack
        {
            let undo_stack = self.undo_stack.lock().await;
            if let Some(operation) = undo_stack.iter().find(|op| op.id == operation_id) {
                return Some(operation.clone());
            }
        }
        
        // Check redo stack
        {
            let redo_stack = self.redo_stack.lock().await;
            if let Some(operation) = redo_stack.iter().find(|op| op.id == operation_id) {
                return Some(operation.clone());
            }
        }
        
        None
    }

    /// Set maximum undo history size
    pub fn set_max_undo_history(&mut self, max_size: usize) {
        self.max_undo_history = max_size;
    }

    /// Get current undo history size
    pub async fn get_undo_history_size(&self) -> usize {
        self.undo_stack.lock().await.len()
    }

    /// Get current redo history size
    pub async fn get_redo_history_size(&self) -> usize {
        self.redo_stack.lock().await.len()
    }
}
