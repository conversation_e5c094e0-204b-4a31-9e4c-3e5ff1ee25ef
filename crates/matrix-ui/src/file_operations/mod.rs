//! File Operations Engine
//!
//! This module provides a comprehensive file operations system with undo/redo support,
//! progress tracking, and atomic operations for MATRIX IDE.

use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::collections::VecDeque;
use std::time::{SystemTime, UNIX_EPOCH};
use serde::{Serialize, Deserialize};
use tokio::sync::{RwLock, Mutex};
use uuid::Uuid;

pub mod operations;
pub mod undo_redo;
pub mod progress;
pub mod context_menu;

use matrix_core::{Engine as CoreEngine, Event};
use crate::error::UiError;

/// File operation types
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum FileOperationType {
    Create { path: PathBuf, is_directory: bool },
    Delete { path: PathBuf },
    Rename { from: PathBuf, to: PathBuf },
    Move { from: PathBuf, to: PathBuf },
    Copy { from: PathBuf, to: PathBuf },
    Duplicate { source: PathBuf, target: PathBuf },
}

/// File operation result
#[derive(Debug, Clone, Serialize)]
pub struct FileOperationResult {
    pub operation_id: Uuid,
    pub operation_type: FileOperationType,
    pub success: bool,
    pub error: Option<String>,
    pub timestamp: u64,
    pub affected_paths: Vec<PathBuf>,
}

/// File operation progress
#[derive(Debug, Clone)]
pub struct FileOperationProgress {
    pub operation_id: Uuid,
    pub operation_type: FileOperationType,
    pub current_file: Option<PathBuf>,
    pub processed_files: usize,
    pub total_files: usize,
    pub bytes_processed: u64,
    pub total_bytes: u64,
    pub percentage: f32,
    pub is_complete: bool,
    pub error: Option<String>,
}

/// Undo/Redo operation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UndoRedoOperation {
    pub id: Uuid,
    pub operation_type: FileOperationType,
    pub reverse_operation: FileOperationType,
    pub timestamp: u64,
    pub description: String,
}

/// File Operations Engine
pub struct FileOperationsEngine {
    /// Core engine reference
    core: Arc<CoreEngine>,
    
    /// Operation history for undo/redo
    undo_stack: Arc<Mutex<VecDeque<UndoRedoOperation>>>,
    redo_stack: Arc<Mutex<VecDeque<UndoRedoOperation>>>,
    
    /// Active operations with progress tracking
    active_operations: Arc<RwLock<std::collections::HashMap<Uuid, FileOperationProgress>>>,
    
    /// Maximum undo history size
    max_undo_history: usize,
    
    /// Operation callbacks
    progress_callbacks: Arc<RwLock<Vec<Box<dyn Fn(FileOperationProgress) + Send + Sync>>>>,
    completion_callbacks: Arc<RwLock<Vec<Box<dyn Fn(FileOperationResult) + Send + Sync>>>>,
}

impl FileOperationsEngine {
    /// Create a new file operations engine
    pub fn new(core: Arc<CoreEngine>) -> Self {
        Self {
            core,
            undo_stack: Arc::new(Mutex::new(VecDeque::new())),
            redo_stack: Arc::new(Mutex::new(VecDeque::new())),
            active_operations: Arc::new(RwLock::new(std::collections::HashMap::new())),
            max_undo_history: 100,
            progress_callbacks: Arc::new(RwLock::new(Vec::new())),
            completion_callbacks: Arc::new(RwLock::new(Vec::new())),
        }
    }

    /// Execute a file operation with progress tracking and undo support
    pub async fn execute_operation(&self, operation: FileOperationType) -> Result<FileOperationResult, UiError> {
        let operation_id = Uuid::new_v4();
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();

        // Initialize progress tracking
        let progress = FileOperationProgress {
            operation_id,
            operation_type: operation.clone(),
            current_file: None,
            processed_files: 0,
            total_files: 1,
            bytes_processed: 0,
            total_bytes: 0,
            percentage: 0.0,
            is_complete: false,
            error: None,
        };

        self.active_operations.write().await.insert(operation_id, progress.clone());
        self.notify_progress(progress.clone()).await;

        // Execute the operation
        let result = match &operation {
            FileOperationType::Create { path, is_directory } => {
                self.create_file_or_directory(operation_id, path, *is_directory).await
            }
            FileOperationType::Delete { path } => {
                self.delete_file_or_directory(operation_id, path).await
            }
            FileOperationType::Rename { from, to } => {
                self.rename_file_or_directory(operation_id, from, to).await
            }
            FileOperationType::Move { from, to } => {
                self.move_file_or_directory(operation_id, from, to).await
            }
            FileOperationType::Copy { from, to } => {
                self.copy_file_or_directory(operation_id, from, to).await
            }
            FileOperationType::Duplicate { source, target } => {
                self.duplicate_file_or_directory(operation_id, source, target).await
            }
        };

        // Update progress to complete
        let mut final_progress = self.active_operations.read().await
            .get(&operation_id)
            .cloned()
            .unwrap_or(progress);
        
        final_progress.is_complete = true;
        final_progress.percentage = 100.0;
        
        if let Err(ref error) = result {
            final_progress.error = Some(error.to_string());
        }

        self.active_operations.write().await.insert(operation_id, final_progress.clone());
        self.notify_progress(final_progress).await;

        // Create result
        let operation_result = match result {
            Ok(affected_paths) => {
                // Add to undo stack if successful
                if let Some(reverse_op) = self.create_reverse_operation(&operation) {
                    let undo_op = UndoRedoOperation {
                        id: operation_id,
                        operation_type: operation.clone(),
                        reverse_operation: reverse_op,
                        timestamp,
                        description: self.operation_description(&operation),
                    };
                    
                    self.add_to_undo_stack(undo_op).await;
                }

                FileOperationResult {
                    operation_id,
                    operation_type: operation,
                    success: true,
                    error: None,
                    timestamp,
                    affected_paths,
                }
            }
            Err(error) => {
                FileOperationResult {
                    operation_id,
                    operation_type: operation,
                    success: false,
                    error: Some(error.to_string()),
                    timestamp,
                    affected_paths: vec![],
                }
            }
        };

        // Notify completion
        self.notify_completion(operation_result.clone()).await;

        // Clean up active operations
        self.active_operations.write().await.remove(&operation_id);

        // Emit event to core engine
        let event = Event::Custom {
            source: "file_operations".to_string(),
            name: "operation_completed".to_string(),
            data: serde_json::to_value(&operation_result).unwrap_or_default(),
        };
        let _ = self.core.event_bus().emit(event);

        if operation_result.success {
            Ok(operation_result)
        } else {
            Err(UiError::FileOperation(operation_result.error.unwrap_or_default()))
        }
    }

    /// Get current operation progress
    pub async fn get_operation_progress(&self, operation_id: Uuid) -> Option<FileOperationProgress> {
        self.active_operations.read().await.get(&operation_id).cloned()
    }

    /// Get all active operations
    pub async fn get_active_operations(&self) -> Vec<FileOperationProgress> {
        self.active_operations.read().await.values().cloned().collect()
    }

    /// Cancel an active operation
    pub async fn cancel_operation(&self, operation_id: Uuid) -> Result<(), UiError> {
        // TODO: Implement operation cancellation
        self.active_operations.write().await.remove(&operation_id);
        Ok(())
    }

    /// Add progress callback
    pub async fn add_progress_callback<F>(&self, callback: F)
    where
        F: Fn(FileOperationProgress) + Send + Sync + 'static,
    {
        self.progress_callbacks.write().await.push(Box::new(callback));
    }

    /// Add completion callback
    pub async fn add_completion_callback<F>(&self, callback: F)
    where
        F: Fn(FileOperationResult) + Send + Sync + 'static,
    {
        self.completion_callbacks.write().await.push(Box::new(callback));
    }

    /// Notify progress callbacks
    async fn notify_progress(&self, progress: FileOperationProgress) {
        let callbacks = self.progress_callbacks.read().await;
        for callback in callbacks.iter() {
            callback(progress.clone());
        }
    }

    /// Notify completion callbacks
    async fn notify_completion(&self, result: FileOperationResult) {
        let callbacks = self.completion_callbacks.read().await;
        for callback in callbacks.iter() {
            callback(result.clone());
        }
    }

    /// Create operation description
    fn operation_description(&self, operation: &FileOperationType) -> String {
        match operation {
            FileOperationType::Create { path, is_directory } => {
                if *is_directory {
                    format!("Create directory: {}", path.display())
                } else {
                    format!("Create file: {}", path.display())
                }
            }
            FileOperationType::Delete { path } => {
                format!("Delete: {}", path.display())
            }
            FileOperationType::Rename { from, to } => {
                format!("Rename: {} → {}", from.display(), to.display())
            }
            FileOperationType::Move { from, to } => {
                format!("Move: {} → {}", from.display(), to.display())
            }
            FileOperationType::Copy { from, to } => {
                format!("Copy: {} → {}", from.display(), to.display())
            }
            FileOperationType::Duplicate { source, target } => {
                format!("Duplicate: {} → {}", source.display(), target.display())
            }
        }
    }
}
