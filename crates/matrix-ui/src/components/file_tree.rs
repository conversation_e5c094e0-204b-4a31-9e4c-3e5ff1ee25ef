//! File Tree Component
//!
//! This module provides a sophisticated file tree component with lazy loading,
//! virtualization, context menus, and drag & drop support.

use std::sync::Arc;
use std::path::{Path, PathBuf};
use std::collections::HashMap;
use floem::{
    reactive::{RwSignal, SignalGet, SignalUpdate, create_rw_signal},
    views::{container, h_stack, v_stack, v_stack_from_iter, label, button, scroll, Decorators},
    event::{Event, EventListener},
    pointer::PointerButton,
    dropped_file::DroppedFileEvent,
    View, IntoView,
    peniko::Color,
};
use serde::{Serialize, Deserialize};

use crate::theme::ThemeManager;
use crate::error::UiError;
use crate::file_operations::context_menu::ContextMenuBuilder;
use crate::file_operations::FileOperationsEngine;
use matrix_core::Engine as CoreEngine;

/// File tree node representing a file or directory
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileTreeNode {
    pub path: PathBuf,
    pub name: String,
    pub is_directory: bool,
    pub is_expanded: bool,
    pub is_selected: bool,
    pub children: Vec<FileTreeNode>,
    pub is_loaded: bool,
    pub size: Option<u64>,
    pub modified: Option<std::time::SystemTime>,
    pub file_type: FileType,
}

/// File type enumeration for icons and behavior
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FileType {
    Directory,
    RustFile,
    JavaScriptFile,
    TypeScriptFile,
    PythonFile,
    JsonFile,
    TomlFile,
    YamlFile,
    MarkdownFile,
    TextFile,
    ImageFile,
    BinaryFile,
    Unknown,
}

/// File tree configuration
#[derive(Debug, Clone)]
pub struct FileTreeConfig {
    pub show_hidden_files: bool,
    pub lazy_loading: bool,
    pub virtualization_enabled: bool,
    pub max_visible_items: usize,
    pub indent_size: f32,
    pub show_file_icons: bool,
    pub show_file_sizes: bool,
    pub show_modified_dates: bool,
}

impl Default for FileTreeConfig {
    fn default() -> Self {
        Self {
            show_hidden_files: false,
            lazy_loading: true,
            virtualization_enabled: true,
            max_visible_items: 1000,
            indent_size: 20.0,
            show_file_icons: true,
            show_file_sizes: false,
            show_modified_dates: false,
        }
    }
}

/// File tree component with advanced features
pub struct FileTree {
    /// Core engine reference
    core: Arc<CoreEngine>,
    
    /// Theme manager
    theme_manager: Arc<ThemeManager>,
    
    /// Root directory path
    root_path: RwSignal<Option<PathBuf>>,
    
    /// File tree data
    tree_data: RwSignal<Vec<FileTreeNode>>,
    
    /// Selected file/directory
    selected_path: RwSignal<Option<PathBuf>>,
    
    /// Expanded directories
    expanded_dirs: RwSignal<HashMap<PathBuf, bool>>,
    
    /// Configuration
    config: RwSignal<FileTreeConfig>,
    
    /// Loading state
    is_loading: RwSignal<bool>,
    
    /// Error state
    error_message: RwSignal<Option<String>>,
    
    /// Filter query
    filter_query: RwSignal<String>,
    
    /// Scroll position for virtualization
    scroll_position: RwSignal<f32>,
    
    /// Visible range for virtualization
    visible_range: RwSignal<(usize, usize)>,

    /// File operations engine
    file_ops: Arc<FileOperationsEngine>,

    /// Currently dragged item
    dragged_item: RwSignal<Option<PathBuf>>,

    /// Drag over target
    drag_over_target: RwSignal<Option<PathBuf>>,

    /// Drag and drop enabled
    drag_drop_enabled: RwSignal<bool>,
}

impl FileTree {
    /// Create a new file tree component
    pub fn new(
        core: Arc<CoreEngine>,
        theme_manager: Arc<ThemeManager>,
        file_ops: Arc<FileOperationsEngine>,
    ) -> Result<Arc<Self>, UiError> {
        Ok(Arc::new(Self {
            core,
            theme_manager,
            file_ops,
            root_path: create_rw_signal(None),
            tree_data: create_rw_signal(Vec::new()),
            selected_path: create_rw_signal(None),
            expanded_dirs: create_rw_signal(HashMap::new()),
            config: create_rw_signal(FileTreeConfig::default()),
            is_loading: create_rw_signal(false),
            error_message: create_rw_signal(None),
            filter_query: create_rw_signal(String::new()),
            scroll_position: create_rw_signal(0.0),
            visible_range: create_rw_signal((0, 100)),
        }))
    }

    /// Create the main view
    pub fn create_view(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let tree_data = self.tree_data;
        let is_loading = self.is_loading;
        let error_message = self.error_message;

        container(
            v_stack((
                // Header with controls
                self.create_header(),
                
                // Main tree area
                container(
                    container(
                        if is_loading.get() {
                            v_stack((
                                label(|| "Loading...")
                                    .style(|s| s.color(Color::rgb8(0x80, 0x80, 0x80))),
                            ))
                        } else if let Some(error) = error_message.get() {
                            v_stack((
                                label(move || format!("Error: {}", error))
                                    .style(|s| s.color(Color::rgb8(0xFF, 0x6B, 0x6B))),
                            ))
                        } else if tree_data.get().is_empty() {
                            v_stack((
                                label(|| "No files to display")
                                    .style(|s| s.color(Color::rgb8(0x80, 0x80, 0x80))),
                            ))
                        } else {
                            v_stack((
                                scroll(
                                    self.create_tree_view()
                                ),
                            ))
                        }
                    )
                )
                .style(|s| s.flex_grow(1.0)),
                
                // Status bar
                self.create_status_bar(),
            ))
        )
        .style(move |s| {
            s.size_full()
             .background(theme.colors.background)
        })
    }

    /// Create the header with controls
    fn create_header(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let filter_query = self.filter_query;

        container(
            h_stack((
                // Refresh button
                button("⟳")
                    .action({
                        let file_tree = self.clone();
                        move || {
                            file_tree.refresh();
                        }
                    })
                    .style(|s| s.margin_right(8.0).padding(4.0)),
                
                // Collapse all button
                button("⊟")
                    .action({
                        let file_tree = self.clone();
                        move || {
                            file_tree.collapse_all();
                        }
                    })
                    .style(|s| s.margin_right(8.0).padding(4.0)),
                
                // Expand all button
                button("⊞")
                    .action({
                        let file_tree = self.clone();
                        move || {
                            file_tree.expand_all();
                        }
                    })
                    .style(|s| s.margin_right(16.0).padding(4.0)),
                
                // Filter input
                floem::views::text_input(filter_query)
                    .placeholder("Filter files...".to_string())
                    .style(move |s| {
                        s.flex_grow(1.0)
                         .padding(4.0)
                         .border(1.0)
                         .border_color(theme.colors.border)
                         .border_radius(4.0)
                    }),
            ))
        )
        .style(move |s| {
            s.width_full()
             .padding(8.0)
             .background(theme.colors.background_secondary)
             .border_bottom(1.0)
             .border_color(theme.colors.border)
        })
    }

    /// Create the tree view
    fn create_tree_view(&self) -> impl View {
        let tree_data = self.tree_data;
        let config = self.config;

        container(
            v_stack_from_iter(
                tree_data.get().into_iter().map(|node| {
                    self.create_tree_node(node, 0)
                })
            )
        )
        .style(|s| s.width_full())
    }

    /// Create a tree node view
    fn create_tree_node(&self, node: FileTreeNode, depth: usize) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let config = self.config.get();
        let indent = depth as f32 * config.indent_size;
        let selected_path = self.selected_path;
        let is_selected = selected_path.get().as_ref() == Some(&node.path);

        container(
            v_stack((
                // Node content
                container(
                    h_stack((
                        // Indentation
                        container(label(|| ""))
                            .style(move |s| s.width(indent)),
                        
                        // Expand/collapse icon
                        container(
                            if node.is_directory {
                                button(if node.is_expanded { "▼" } else { "▶" })
                                    .action({
                                        let file_tree = self.clone();
                                        let node_path = node.path.clone();
                                        move || {
                                            file_tree.toggle_directory(&node_path);
                                        }
                                    })
                                    .style(|s| s.font_size(12.0).margin_right(4.0).padding(2.0))
                            } else {
                                button("")
                                    .style(|s| s.font_size(12.0).margin_right(4.0).padding(2.0).width(16.0))
                            }
                        ),
                        
                        // File icon
                        label({
                            let icon = self.get_file_icon(&node.file_type);
                            move || icon
                        })
                        .style(|s| s.margin_right(4.0)),
                        
                        // File name
                        label(move || node.name.clone())
                            .style(move |s| {
                                let base_style = s.flex_grow(1.0);
                                if is_selected {
                                    base_style.color(theme.colors.accent)
                                } else {
                                    base_style.color(theme.colors.text)
                                }
                            }),
                        
                        // File size (if enabled)
                        container(
                            if config.show_file_sizes && !node.is_directory {
                                label(move || {
                                    node.size.map(|s| format_file_size(s)).unwrap_or_else(|| "-".to_string())
                                })
                                .style(|s| s.font_size(12.0).color(Color::rgb8(0xA0, 0xA0, 0xA0)).margin_left(8.0))
                            } else {
                                label(|| "")
                                    .style(|s| s.width(0.0))
                            }
                        ),
                    ))
                )
                .style(move |s| {
                    let base_style = s
                        .width_full()
                        .padding_vert(2.0)
                        .padding_horiz(4.0)
                        .hover(|s| s.background(theme.colors.background_tertiary));
                    
                    if is_selected {
                        base_style.background(theme.colors.background_secondary)
                    } else {
                        base_style
                    }
                })
                .on_click_stop({
                    let file_tree = self.clone();
                    let node_path = node.path.clone();
                    move |_| {
                        file_tree.select_path(&node_path);
                    }
                })
                .on_event(EventListener::PointerDown, {
                    let file_tree = self.clone();
                    let node_path = node.path.clone();
                    let is_directory = node.is_directory;
                    move |event| {
                        if let Event::PointerDown(pointer_event) = event {
                            if pointer_event.button == PointerButton::Secondary {
                                // Right-click detected - show context menu
                                file_tree.show_context_menu(&node_path, is_directory, pointer_event.pos.x, pointer_event.pos.y);
                            }
                        }
                        floem::event::EventPropagation::Continue
                    }
                }),
                
                // Children (if expanded)
                if node.is_directory && node.is_expanded {
                    container(
                        v_stack_from_iter(
                            node.children.into_iter().map(|child| {
                                self.create_tree_node(child, depth + 1)
                            })
                        )
                    )
                } else {
                    container(label(|| ""))
                        .style(|s| s.height(0.0))
                },
            ))
        )
    }

    /// Create status bar
    fn create_status_bar(&self) -> impl View {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();
        let tree_data = self.tree_data;
        let selected_path = self.selected_path;

        container(
            h_stack((
                label({
                    let tree_data = self.tree_data;
                    move || {
                        let count = tree_data.get().len(); // Simplified count
                        format!("{} items", count)
                    }
                })
                .style(|s| s.font_size(12.0).margin_right(16.0)),
                
                label({
                    move || {
                        selected_path.get()
                            .map(|p| p.to_string_lossy().to_string())
                            .unwrap_or_else(|| "No selection".to_string())
                    }
                })
                .style(|s| s.font_size(12.0).flex_grow(1.0)),
            ))
        )
        .style(move |s| {
            s.width_full()
             .padding(8.0)
             .background(theme.colors.background_secondary)
             .border_top(1.0)
             .border_color(theme.colors.border)
        })
    }

    /// Get file icon based on file type
    fn get_file_icon(&self, file_type: &FileType) -> &'static str {
        match file_type {
            FileType::Directory => "📁",
            FileType::RustFile => "🦀",
            FileType::JavaScriptFile => "📜",
            FileType::TypeScriptFile => "📘",
            FileType::PythonFile => "🐍",
            FileType::JsonFile => "📋",
            FileType::TomlFile => "⚙️",
            FileType::YamlFile => "📄",
            FileType::MarkdownFile => "📝",
            FileType::TextFile => "📄",
            FileType::ImageFile => "🖼️",
            FileType::BinaryFile => "⚙️",
            FileType::Unknown => "📄",
        }
    }

    /// Count total items in tree
    fn count_items(&self, nodes: &[FileTreeNode]) -> usize {
        nodes.iter().map(|node| {
            1 + if node.is_expanded {
                self.count_items(&node.children)
            } else {
                0
            }
        }).sum()
    }

    /// Show context menu for file/directory
    fn show_context_menu(&self, path: &Path, is_directory: bool, x: f64, y: f64) {
        let builder = ContextMenuBuilder::new(self.file_ops.clone(), self.core.clone());

        let context_menu = if is_directory {
            builder.build_for_directory(path)
        } else {
            builder.build_for_file(path)
        };

        context_menu.show_at_position(x, y);
    }

    /// Set root directory
    pub fn set_root_directory(&self, path: PathBuf) -> Result<(), UiError> {
        self.root_path.set(Some(path.clone()));
        self.load_directory(path)?;
        Ok(())
    }

    /// Load directory contents
    fn load_directory(&self, path: PathBuf) -> Result<(), UiError> {
        self.is_loading.set(true);
        self.error_message.set(None);

        // TODO: Implement actual directory loading
        // For now, create dummy data
        let dummy_nodes = vec![
            FileTreeNode {
                path: path.join("src"),
                name: "src".to_string(),
                is_directory: true,
                is_expanded: false,
                is_selected: false,
                children: vec![],
                is_loaded: false,
                size: None,
                modified: None,
                file_type: FileType::Directory,
            },
            FileTreeNode {
                path: path.join("Cargo.toml"),
                name: "Cargo.toml".to_string(),
                is_directory: false,
                is_expanded: false,
                is_selected: false,
                children: vec![],
                is_loaded: true,
                size: Some(1024),
                modified: None,
                file_type: FileType::TomlFile,
            },
        ];

        self.tree_data.set(dummy_nodes);
        self.is_loading.set(false);
        Ok(())
    }

    /// Toggle directory expansion
    fn toggle_directory(&self, path: &Path) {
        let mut expanded = self.expanded_dirs.get();
        let is_expanded = expanded.get(path).copied().unwrap_or(false);
        expanded.insert(path.to_path_buf(), !is_expanded);
        self.expanded_dirs.set(expanded);

        // Update tree data
        let mut tree_data = self.tree_data.get();
        self.update_node_expansion(&mut tree_data, path, !is_expanded);
        self.tree_data.set(tree_data);
    }

    /// Update node expansion state
    fn update_node_expansion(&self, nodes: &mut [FileTreeNode], path: &Path, expanded: bool) {
        for node in nodes {
            if node.path == path {
                node.is_expanded = expanded;
                if expanded && !node.is_loaded {
                    // TODO: Load children lazily
                    node.is_loaded = true;
                }
                return;
            }
            if node.is_expanded {
                self.update_node_expansion(&mut node.children, path, expanded);
            }
        }
    }

    /// Select a path
    fn select_path(&self, path: &Path) {
        self.selected_path.set(Some(path.to_path_buf()));
    }

    /// Refresh the tree
    fn refresh(&self) {
        if let Some(root) = self.root_path.get() {
            let _ = self.load_directory(root);
        }
    }

    /// Collapse all directories
    fn collapse_all(&self) {
        self.expanded_dirs.set(HashMap::new());
        let mut tree_data = self.tree_data.get();
        self.collapse_all_nodes(&mut tree_data);
        self.tree_data.set(tree_data);
    }

    /// Expand all directories
    fn expand_all(&self) {
        let mut tree_data = self.tree_data.get();
        self.expand_all_nodes(&mut tree_data);
        self.tree_data.set(tree_data);
    }

    /// Collapse all nodes recursively
    fn collapse_all_nodes(&self, nodes: &mut [FileTreeNode]) {
        for node in nodes {
            if node.is_directory {
                node.is_expanded = false;
                self.collapse_all_nodes(&mut node.children);
            }
        }
    }

    /// Expand all nodes recursively
    fn expand_all_nodes(&self, nodes: &mut [FileTreeNode]) {
        for node in nodes {
            if node.is_directory {
                node.is_expanded = true;
                node.is_loaded = true;
                self.expand_all_nodes(&mut node.children);
            }
        }
    }

    /// Get selected path
    pub fn get_selected_path(&self) -> Option<PathBuf> {
        self.selected_path.get()
    }

    /// Set filter query
    pub fn set_filter(&self, query: String) {
        self.filter_query.set(query);
    }
}

impl Clone for FileTree {
    fn clone(&self) -> Self {
        Self {
            core: self.core.clone(),
            theme_manager: self.theme_manager.clone(),
            file_ops: self.file_ops.clone(),
            root_path: self.root_path,
            tree_data: self.tree_data,
            selected_path: self.selected_path,
            expanded_dirs: self.expanded_dirs,
            config: self.config,
            is_loading: self.is_loading,
            error_message: self.error_message,
            filter_query: self.filter_query,
            scroll_position: self.scroll_position,
            visible_range: self.visible_range,
        }
    }
}

/// Format file size for display
fn format_file_size(size: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = size as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", size as u64, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}
