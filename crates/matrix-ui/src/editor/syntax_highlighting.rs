//! Syntax Highlighting System for Advanced Editor
//!
//! This module provides comprehensive syntax highlighting capabilities
//! for various programming languages in the MATRIX IDE.

use std::collections::HashMap;
use std::sync::Arc;
use regex::Regex;
use serde::{Serialize, Deserialize};

use crate::theme::{Theme, ThemeManager};
use crate::error::UiError;

/// Token types for syntax highlighting
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum TokenType {
    /// Keywords (if, else, fn, etc.)
    Keyword,
    /// String literals
    String,
    /// Numeric literals
    Number,
    /// Comments
    Comment,
    /// Identifiers/variables
    Identifier,
    /// Function names
    Function,
    /// Type names
    Type,
    /// Operators (+, -, *, etc.)
    Operator,
    /// Punctuation (;, ,, etc.)
    Punctuation,
    /// Preprocessor directives
    Preprocessor,
    /// Constants
    Constant,
    /// Built-in types/functions
    Builtin,
    /// Error tokens
    Error,
    /// Whitespace
    Whitespace,
}

/// Syntax token with position and type
#[derive(Debug, Clone)]
pub struct SyntaxToken {
    /// Token type
    pub token_type: TokenType,
    /// Start position in text (byte offset)
    pub start: usize,
    /// End position in text (byte offset)
    pub end: usize,
    /// Token text
    pub text: String,
    /// Line number (0-based)
    pub line: usize,
    /// Column number (0-based)
    pub column: usize,
}

/// Language-specific syntax rules
#[derive(Debug, Clone)]
pub struct LanguageSyntax {
    /// Language name
    pub name: String,
    /// File extensions
    pub extensions: Vec<String>,
    /// Keyword patterns
    pub keywords: Vec<String>,
    /// String patterns
    pub string_patterns: Vec<Regex>,
    /// Comment patterns
    pub comment_patterns: Vec<Regex>,
    /// Number patterns
    pub number_patterns: Vec<Regex>,
    /// Function patterns
    pub function_patterns: Vec<Regex>,
    /// Type patterns
    pub type_patterns: Vec<Regex>,
    /// Operator patterns
    pub operator_patterns: Vec<Regex>,
    /// Built-in patterns
    pub builtin_patterns: Vec<Regex>,
}

/// Syntax highlighter
pub struct SyntaxHighlighter {
    /// Language syntaxes
    languages: HashMap<String, LanguageSyntax>,
    /// Theme manager
    theme_manager: Arc<ThemeManager>,
    /// Token cache
    token_cache: HashMap<String, Vec<SyntaxToken>>,
}

impl SyntaxHighlighter {
    /// Create a new syntax highlighter
    pub fn new(theme_manager: Arc<ThemeManager>) -> Self {
        let mut highlighter = Self {
            languages: HashMap::new(),
            theme_manager,
            token_cache: HashMap::new(),
        };
        
        // Register default languages
        highlighter.register_default_languages();
        
        highlighter
    }

    /// Register default language syntaxes
    fn register_default_languages(&mut self) {
        // Rust syntax
        self.register_language(LanguageSyntax {
            name: "rust".to_string(),
            extensions: vec!["rs".to_string()],
            keywords: vec![
                "fn", "let", "mut", "const", "static", "if", "else", "match", "for", "while", "loop",
                "break", "continue", "return", "struct", "enum", "impl", "trait", "mod", "use", "pub",
                "crate", "super", "self", "Self", "where", "async", "await", "move", "ref", "in",
                "as", "dyn", "unsafe", "extern", "type", "union",
            ].into_iter().map(String::from).collect(),
            string_patterns: vec![
                Regex::new(r#""([^"\\]|\\.)*""#).unwrap(),
                Regex::new(r#"'([^'\\]|\\.)*'"#).unwrap(),
                Regex::new(r"r#+[^#]*#+").unwrap(),
            ],
            comment_patterns: vec![
                Regex::new(r"//.*$").unwrap(),
                Regex::new(r"/\*[\s\S]*?\*/").unwrap(),
            ],
            number_patterns: vec![
                Regex::new(r"\b\d+\.?\d*([eE][+-]?\d+)?\b").unwrap(),
                Regex::new(r"\b0x[0-9a-fA-F]+\b").unwrap(),
                Regex::new(r"\b0b[01]+\b").unwrap(),
                Regex::new(r"\b0o[0-7]+\b").unwrap(),
            ],
            function_patterns: vec![
                Regex::new(r"\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(").unwrap(),
            ],
            type_patterns: vec![
                Regex::new(r"\b[A-Z][a-zA-Z0-9_]*\b").unwrap(),
            ],
            operator_patterns: vec![
                Regex::new(r"[+\-*/%=<>!&|^~]").unwrap(),
            ],
            builtin_patterns: vec![
                Regex::new(r"\b(println!|print!|vec!|format!|panic!|assert!|debug_assert!)\b").unwrap(),
                Regex::new(r"\b(String|Vec|HashMap|Option|Result|Box|Rc|Arc)\b").unwrap(),
            ],
        });

        // JavaScript/TypeScript syntax
        self.register_language(LanguageSyntax {
            name: "javascript".to_string(),
            extensions: vec!["js".to_string(), "jsx".to_string(), "ts".to_string(), "tsx".to_string()],
            keywords: vec![
                "var", "let", "const", "function", "return", "if", "else", "for", "while", "do",
                "break", "continue", "switch", "case", "default", "try", "catch", "finally",
                "throw", "new", "delete", "typeof", "instanceof", "in", "of", "class", "extends",
                "super", "static", "import", "export", "from", "as", "async", "await", "yield",
                "interface", "type", "enum", "namespace", "module", "declare", "public", "private",
                "protected", "readonly", "abstract",
            ].into_iter().map(String::from).collect(),
            string_patterns: vec![
                Regex::new(r#""([^"\\]|\\.)*""#).unwrap(),
                Regex::new(r#"'([^'\\]|\\.)*'"#).unwrap(),
                Regex::new(r"`([^`\\]|\\.)*`").unwrap(),
            ],
            comment_patterns: vec![
                Regex::new(r"//.*$").unwrap(),
                Regex::new(r"/\*[\s\S]*?\*/").unwrap(),
            ],
            number_patterns: vec![
                Regex::new(r"\b\d+\.?\d*([eE][+-]?\d+)?\b").unwrap(),
                Regex::new(r"\b0x[0-9a-fA-F]+\b").unwrap(),
                Regex::new(r"\b0b[01]+\b").unwrap(),
                Regex::new(r"\b0o[0-7]+\b").unwrap(),
            ],
            function_patterns: vec![
                Regex::new(r"\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(").unwrap(),
            ],
            type_patterns: vec![
                Regex::new(r"\b[A-Z][a-zA-Z0-9_]*\b").unwrap(),
            ],
            operator_patterns: vec![
                Regex::new(r"[+\-*/%=<>!&|^~?:]").unwrap(),
            ],
            builtin_patterns: vec![
                Regex::new(r"\b(console|window|document|Array|Object|String|Number|Boolean|Date|RegExp|Promise)\b").unwrap(),
            ],
        });

        // Python syntax
        self.register_language(LanguageSyntax {
            name: "python".to_string(),
            extensions: vec!["py".to_string(), "pyw".to_string()],
            keywords: vec![
                "and", "as", "assert", "break", "class", "continue", "def", "del", "elif", "else",
                "except", "exec", "finally", "for", "from", "global", "if", "import", "in", "is",
                "lambda", "not", "or", "pass", "print", "raise", "return", "try", "while", "with",
                "yield", "async", "await", "nonlocal",
            ].into_iter().map(String::from).collect(),
            string_patterns: vec![
                Regex::new(r#""([^"\\]|\\.)*""#).unwrap(),
                Regex::new(r#"'([^'\\]|\\.)*'"#).unwrap(),
                Regex::new(r#""""[\s\S]*?""""#).unwrap(),
                Regex::new(r#"'''[\s\S]*?'''"#).unwrap(),
            ],
            comment_patterns: vec![
                Regex::new(r"#.*$").unwrap(),
            ],
            number_patterns: vec![
                Regex::new(r"\b\d+\.?\d*([eE][+-]?\d+)?\b").unwrap(),
                Regex::new(r"\b0x[0-9a-fA-F]+\b").unwrap(),
                Regex::new(r"\b0b[01]+\b").unwrap(),
                Regex::new(r"\b0o[0-7]+\b").unwrap(),
            ],
            function_patterns: vec![
                Regex::new(r"\bdef\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\(").unwrap(),
                Regex::new(r"\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(").unwrap(),
            ],
            type_patterns: vec![
                Regex::new(r"\bclass\s+([A-Z][a-zA-Z0-9_]*)\b").unwrap(),
            ],
            operator_patterns: vec![
                Regex::new(r"[+\-*/%=<>!&|^~]").unwrap(),
            ],
            builtin_patterns: vec![
                Regex::new(r"\b(print|len|range|enumerate|zip|map|filter|sorted|reversed|sum|min|max|abs|round)\b").unwrap(),
                Regex::new(r"\b(str|int|float|bool|list|dict|tuple|set|frozenset|bytes|bytearray)\b").unwrap(),
            ],
        });
    }

    /// Register a language syntax
    pub fn register_language(&mut self, syntax: LanguageSyntax) {
        self.languages.insert(syntax.name.clone(), syntax);
    }

    /// Tokenize text for syntax highlighting
    pub fn tokenize(&mut self, text: &str, language: &str) -> Result<Vec<SyntaxToken>, UiError> {
        // Check cache first
        let cache_key = format!("{}:{}", language, text.len());
        if let Some(cached_tokens) = self.token_cache.get(&cache_key) {
            return Ok(cached_tokens.clone());
        }

        let syntax = self.languages.get(language)
            .ok_or_else(|| UiError::SyntaxError(format!("Unknown language: {}", language)))?;

        let mut tokens = Vec::new();
        let mut position = 0;
        let mut line = 0;
        let mut column = 0;

        while position < text.len() {
            let remaining = &text[position..];
            let mut matched = false;

            // Try to match different token types
            if let Some(token) = self.match_comment(remaining, position, line, column, syntax) {
                position = token.end;
                self.update_position(&token.text, &mut line, &mut column);
                tokens.push(token);
                matched = true;
            } else if let Some(token) = self.match_string(remaining, position, line, column, syntax) {
                position = token.end;
                self.update_position(&token.text, &mut line, &mut column);
                tokens.push(token);
                matched = true;
            } else if let Some(token) = self.match_number(remaining, position, line, column, syntax) {
                position = token.end;
                self.update_position(&token.text, &mut line, &mut column);
                tokens.push(token);
                matched = true;
            } else if let Some(token) = self.match_keyword(remaining, position, line, column, syntax) {
                position = token.end;
                self.update_position(&token.text, &mut line, &mut column);
                tokens.push(token);
                matched = true;
            } else if let Some(token) = self.match_builtin(remaining, position, line, column, syntax) {
                position = token.end;
                self.update_position(&token.text, &mut line, &mut column);
                tokens.push(token);
                matched = true;
            } else if let Some(token) = self.match_function(remaining, position, line, column, syntax) {
                position = token.end;
                self.update_position(&token.text, &mut line, &mut column);
                tokens.push(token);
                matched = true;
            } else if let Some(token) = self.match_type(remaining, position, line, column, syntax) {
                position = token.end;
                self.update_position(&token.text, &mut line, &mut column);
                tokens.push(token);
                matched = true;
            } else if let Some(token) = self.match_operator(remaining, position, line, column, syntax) {
                position = token.end;
                self.update_position(&token.text, &mut line, &mut column);
                tokens.push(token);
                matched = true;
            }

            if !matched {
                // Handle single character or whitespace
                let ch = remaining.chars().next().unwrap();
                let token_type = if ch.is_whitespace() {
                    TokenType::Whitespace
                } else if ch.is_alphabetic() || ch == '_' {
                    TokenType::Identifier
                } else {
                    TokenType::Punctuation
                };

                let token = SyntaxToken {
                    token_type,
                    start: position,
                    end: position + ch.len_utf8(),
                    text: ch.to_string(),
                    line,
                    column,
                };

                position = token.end;
                self.update_position(&token.text, &mut line, &mut column);
                tokens.push(token);
            }
        }

        // Cache the result
        self.token_cache.insert(cache_key, tokens.clone());

        Ok(tokens)
    }

    /// Match comment patterns
    fn match_comment(&self, text: &str, start: usize, line: usize, column: usize, syntax: &LanguageSyntax) -> Option<SyntaxToken> {
        for pattern in &syntax.comment_patterns {
            if let Some(mat) = pattern.find(text) {
                if mat.start() == 0 {
                    return Some(SyntaxToken {
                        token_type: TokenType::Comment,
                        start,
                        end: start + mat.end(),
                        text: mat.as_str().to_string(),
                        line,
                        column,
                    });
                }
            }
        }
        None
    }

    /// Match string patterns
    fn match_string(&self, text: &str, start: usize, line: usize, column: usize, syntax: &LanguageSyntax) -> Option<SyntaxToken> {
        for pattern in &syntax.string_patterns {
            if let Some(mat) = pattern.find(text) {
                if mat.start() == 0 {
                    return Some(SyntaxToken {
                        token_type: TokenType::String,
                        start,
                        end: start + mat.end(),
                        text: mat.as_str().to_string(),
                        line,
                        column,
                    });
                }
            }
        }
        None
    }

    /// Match number patterns
    fn match_number(&self, text: &str, start: usize, line: usize, column: usize, syntax: &LanguageSyntax) -> Option<SyntaxToken> {
        for pattern in &syntax.number_patterns {
            if let Some(mat) = pattern.find(text) {
                if mat.start() == 0 {
                    return Some(SyntaxToken {
                        token_type: TokenType::Number,
                        start,
                        end: start + mat.end(),
                        text: mat.as_str().to_string(),
                        line,
                        column,
                    });
                }
            }
        }
        None
    }

    /// Match keyword patterns
    fn match_keyword(&self, text: &str, start: usize, line: usize, column: usize, syntax: &LanguageSyntax) -> Option<SyntaxToken> {
        for keyword in &syntax.keywords {
            if text.starts_with(keyword) {
                // Check word boundary
                let end_pos = keyword.len();
                if end_pos == text.len() || !text.chars().nth(end_pos).unwrap().is_alphanumeric() {
                    return Some(SyntaxToken {
                        token_type: TokenType::Keyword,
                        start,
                        end: start + end_pos,
                        text: keyword.clone(),
                        line,
                        column,
                    });
                }
            }
        }
        None
    }

    /// Match builtin patterns
    fn match_builtin(&self, text: &str, start: usize, line: usize, column: usize, syntax: &LanguageSyntax) -> Option<SyntaxToken> {
        for pattern in &syntax.builtin_patterns {
            if let Some(mat) = pattern.find(text) {
                if mat.start() == 0 {
                    return Some(SyntaxToken {
                        token_type: TokenType::Builtin,
                        start,
                        end: start + mat.end(),
                        text: mat.as_str().to_string(),
                        line,
                        column,
                    });
                }
            }
        }
        None
    }

    /// Match function patterns
    fn match_function(&self, text: &str, start: usize, line: usize, column: usize, syntax: &LanguageSyntax) -> Option<SyntaxToken> {
        for pattern in &syntax.function_patterns {
            if let Some(mat) = pattern.find(text) {
                if mat.start() == 0 {
                    if let Some(captures) = pattern.captures(text) {
                        if let Some(func_name) = captures.get(1) {
                            return Some(SyntaxToken {
                                token_type: TokenType::Function,
                                start,
                                end: start + func_name.end(),
                                text: func_name.as_str().to_string(),
                                line,
                                column,
                            });
                        }
                    }
                }
            }
        }
        None
    }

    /// Match type patterns
    fn match_type(&self, text: &str, start: usize, line: usize, column: usize, syntax: &LanguageSyntax) -> Option<SyntaxToken> {
        for pattern in &syntax.type_patterns {
            if let Some(mat) = pattern.find(text) {
                if mat.start() == 0 {
                    return Some(SyntaxToken {
                        token_type: TokenType::Type,
                        start,
                        end: start + mat.end(),
                        text: mat.as_str().to_string(),
                        line,
                        column,
                    });
                }
            }
        }
        None
    }

    /// Match operator patterns
    fn match_operator(&self, text: &str, start: usize, line: usize, column: usize, syntax: &LanguageSyntax) -> Option<SyntaxToken> {
        for pattern in &syntax.operator_patterns {
            if let Some(mat) = pattern.find(text) {
                if mat.start() == 0 {
                    return Some(SyntaxToken {
                        token_type: TokenType::Operator,
                        start,
                        end: start + mat.end(),
                        text: mat.as_str().to_string(),
                        line,
                        column,
                    });
                }
            }
        }
        None
    }

    /// Update line and column position
    fn update_position(&self, text: &str, line: &mut usize, column: &mut usize) {
        for ch in text.chars() {
            if ch == '\n' {
                *line += 1;
                *column = 0;
            } else {
                *column += 1;
            }
        }
    }

    /// Get color for token type from theme
    pub fn get_token_color(&self, token_type: TokenType) -> floem::peniko::Color {
        let theme = self.theme_manager.get_active_theme().unwrap_or_default();

        match token_type {
            TokenType::Keyword => theme.syntax.keyword,
            TokenType::String => theme.syntax.string,
            TokenType::Number => theme.syntax.number,
            TokenType::Comment => theme.syntax.comment,
            TokenType::Identifier => theme.syntax.identifier,
            TokenType::Function => theme.syntax.function,
            TokenType::Type => theme.syntax.type_name,
            TokenType::Operator => theme.syntax.operator,
            TokenType::Punctuation => theme.syntax.punctuation,
            TokenType::Preprocessor => theme.syntax.preprocessor,
            TokenType::Constant => theme.syntax.constant,
            TokenType::Builtin => theme.syntax.builtin,
            TokenType::Error => theme.syntax.error,
            TokenType::Whitespace => theme.colors.text,
        }
    }

    /// Clear token cache
    pub fn clear_cache(&mut self) {
        self.token_cache.clear();
    }

    /// Get supported languages
    pub fn get_supported_languages(&self) -> Vec<String> {
        self.languages.keys().cloned().collect()
    }

    /// Detect language from file extension
    pub fn detect_language(&self, file_path: &std::path::Path) -> Option<String> {
        let extension = file_path.extension()?.to_str()?;
        
        for (name, syntax) in &self.languages {
            if syntax.extensions.contains(&extension.to_string()) {
                return Some(name.clone());
            }
        }
        
        None
    }
}
